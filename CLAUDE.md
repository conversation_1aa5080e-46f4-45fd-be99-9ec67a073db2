# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Firefox browser extension called "YouTube Video Bookmarker" that allows users to:

- Save YouTube videos via context menu right-click
- Extract video transcripts using Apify actor API
- View saved videos in a popup panel
- Open transcript pages in new browser tabs

## Development Commands

**Extension Development:**
```bash
# Install Firefox extension development tool globally
npm install --global web-ext

# Run extension with auto-reload during development
web-ext run

# Build extension for distribution
web-ext build

# Load extension in Firefox for manual testing
# Navigate to about:debugging -> This Firefox -> Load Temporary Add-on
# Select manifest.json file
```

**Testing:**
```bash
# Open debug/extension-status.html in browser to check extension functionality
# Run test/console-test.js in browser console on any YouTube page
# Check browser console for "[YT-Bookmarker]" debug logs
```

**Debugging Context Menu Issues:**
```bash
# Use debug/debug-context-menu.js for detailed context menu analysis
# Check DEBUG_CONTEXT_MENU.md and TROUBLESHOOTING.md for specific issues
```

## Architecture Overview

### Core Extension Structure (Manifest V2)

**Background Scripts (Event Pages):**
- `background/background.js` - Main service worker handling context menus, video processing, and storage
- `background/apify-transcript.js` - Apify API integration for transcript extraction

**Content Scripts:**
- `content/content.js` - YouTube page interaction and video title extraction  
- `content/context-menu-injector.js` - Enhanced context menu detection and video ID extraction

**UI Components:**
- `ui/panel.html/css/js` - Extension popup interface for viewing saved videos

### Key Architectural Patterns

**Multi-Layer Video Detection System:**
1. **URL Pattern Matching** - Extracts video IDs from YouTube URLs (watch, shorts, embed)
2. **Context Menu Integration** - Single comprehensive menu works across all contexts (page, link, image, video, frame)  
3. **DOM Analysis** - Content script analyzes page elements for video metadata
4. **Enhanced Context Detection** - Advanced selectors and hover tracking for thumbnails

**Transcript Processing Pipeline:**
1. **Apify Actor API** - Uses `pintostudio~youtube-transcript-scraper` actor for transcript extraction
2. **Async Processing** - Background jobs with status tracking (queued → processing → complete/error)
3. **Blob URL Generation** - Firefox-compatible transcript page creation using blob URLs
4. **Storage Integration** - Transcripts cached in browser.storage.local with video metadata

**Storage Schema:**
```javascript
{
  videos: [
    {
      id: "videoId",
      url: "https://youtube.com/watch?v=...",
      title: "Video Title", 
      thumbnailUrl: "https://i.ytimg.com/vi/.../mqdefault.jpg",
      transcriptStatus: "queued|processing|success|error",
      transcript: "transcript text",
      dateAdded: timestamp
    }
  ]
}
```

### Context Menu Implementation Details

**Single Comprehensive Menu Strategy:**
- One context menu item works across all contexts to avoid Firefox limitations
- Handles YouTube watch pages, shorts, thumbnails, and embedded videos
- Multiple fallback video ID extraction methods for robustness

**Video ID Extraction Hierarchy:**
1. Direct URL extraction from context (linkUrl, srcUrl, pageUrl)
2. Content script DOM analysis for current page video
3. Enhanced context detection via injector script
4. Fallback to tab URL parsing

### External API Integration

**Apify Transcript Service:**
- Actor ID: `faVsWy9VTSNVIhWpR` (pintostudio~youtube-transcript-scraper)
- Polling-based async job processing with 60-second timeout
- Handles multiple YouTube transcript formats (JSON3, XML, SRV3)
- Built-in retry logic and error handling

**Required Permissions:**
- `storage` - Video data persistence
- `contextMenus` - Right-click menu integration  
- `activeTab` - Current tab access for content scripts
- `tabs` - Tab management for opening transcript pages
- `*://*.youtube.com/*` - YouTube page access
- `https://api.apify.com/*` - Transcript API access

## Firefox Extension Standards

**Manifest V2 Compliance:**
- Uses non-persistent background scripts (`"persistent": false`)
- Event pages pattern instead of service workers
- `browser.*` API namespace for Firefox compatibility
- WebExtensions API standard (2024-2025)

**Security Considerations:**
- API keys stored in extension code (needs user configuration system)
- Blob URL usage for transcript pages (Firefox security model)
- Content Security Policy compliance
- Minimal required permissions

## Testing & Debugging

**Built-in Test Suite:**
- `test/console-test.js` - Comprehensive browser console test suite
- `debug/extension-status.html` - Extension functionality checker
- `debug/debug-context-menu.js` - Context menu debugging tools

**Debug Logging:**
- All components use `[YT-Bookmarker]` prefixed console logs
- Timestamp-based logging for easy tracking
- Context menu detailed logging with full API context

**Known Issues:**
- Context menu may not appear on some video thumbnail elements
- Requires enhanced DOM analysis for complex YouTube layouts
- Apify API dependency for transcript extraction

## File Organization

```
youtube-summarizer-plugin/
├── manifest.json              # Extension configuration (Manifest V2)
├── background/
│   ├── background.js          # Main background script with context menu & storage
│   └── apify-transcript.js    # Apify API client for transcript extraction
├── content/
│   ├── content.js            # YouTube page video title extraction
│   └── context-menu-injector.js  # Enhanced video detection for context menus
├── ui/
│   ├── panel.html/css/js     # Extension popup for viewing saved videos
├── debug/
│   ├── debug-context-menu.js # Context menu debugging utilities  
│   └── extension-status.html # Extension health checker
├── test/
│   └── console-test.js       # Browser console test suite
└── icons/                    # Extension icons
```