// Debug script to test context menu behavior on YouTube
// This script can be run in the browser console on YouTube pages to test video ID extraction

(function() {
    'use strict';
    
    console.log('=== YouTube Context Menu Debug Script ===');
    
    // Function to extract video ID from URL (same as in background.js)
    function getVideoId(url) {
        if (!url) return null;
        const youtubeRegex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=|shorts\/)|youtu\.be\/)([^"&?\/\s]{11})/;
        const match = url.match(youtubeRegex);
        return (match && match[1]) ? match[1] : null;
    }
    
    // Function to find all video elements and their context
    function analyzeVideoElements() {
        console.log('\n--- Analyzing Video Elements ---');
        const videos = document.querySelectorAll('video');
        console.log(`Found ${videos.length} video elements`);
        
        videos.forEach((video, index) => {
            console.log(`\nVideo ${index + 1}:`);
            console.log('- Element:', video);
            console.log('- src:', video.src);
            console.log('- currentSrc:', video.currentSrc);
            console.log('- poster:', video.poster);
            
            // Check parent elements for video ID
            let parent = video.parentElement;
            let level = 0;
            while (parent && level < 5) {
                const dataVideoId = parent.getAttribute('data-video-id') || 
                                  parent.getAttribute('data-context-item-id');
                if (dataVideoId) {
                    console.log(`- Found data-video-id at parent level ${level}:`, dataVideoId);
                }
                
                const href = parent.getAttribute('href');
                if (href) {
                    const videoId = getVideoId(href);
                    if (videoId) {
                        console.log(`- Found video ID in parent href at level ${level}:`, videoId);
                    }
                }
                
                parent = parent.parentElement;
                level++;
            }
            
            // Check for nearby links
            const nearbyLinks = video.parentElement?.querySelectorAll('a[href*="/watch"]') || [];
            if (nearbyLinks.length > 0) {
                console.log(`- Found ${nearbyLinks.length} nearby video links:`);
                nearbyLinks.forEach((link, linkIndex) => {
                    const videoId = getVideoId(link.href);
                    console.log(`  Link ${linkIndex + 1}: ${link.href} -> ${videoId}`);
                });
            }
        });
    }
    
    // Function to find all elements with video-related data attributes
    function analyzeVideoDataAttributes() {
        console.log('\n--- Analyzing Elements with Video Data Attributes ---');
        const selectors = [
            '[data-video-id]',
            '[data-context-item-id]',
            '[data-video-title]'
        ];
        
        selectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            console.log(`\n${selector}: ${elements.length} elements`);
            elements.forEach((el, index) => {
                if (index < 5) { // Limit output
                    console.log(`- Element ${index + 1}:`, {
                        tagName: el.tagName,
                        dataVideoId: el.getAttribute('data-video-id'),
                        dataContextItemId: el.getAttribute('data-context-item-id'),
                        dataVideoTitle: el.getAttribute('data-video-title'),
                        href: el.getAttribute('href')
                    });
                }
            });
        });
    }
    
    // Function to simulate context menu click and test extraction
    function testVideoIdExtraction() {
        console.log('\n--- Testing Video ID Extraction Methods ---');
        
        // Test current page URL
        const pageVideoId = getVideoId(window.location.href);
        console.log('Page URL video ID:', pageVideoId);
        
        // Test all video elements
        const videos = document.querySelectorAll('video');
        videos.forEach((video, index) => {
            console.log(`\nTesting video element ${index + 1}:`);
            
            // Method 1: Direct src
            const srcVideoId = getVideoId(video.src);
            console.log('- From src:', srcVideoId);
            
            // Method 2: Parent data attributes
            let parent = video.parentElement;
            let foundDataId = null;
            while (parent && !foundDataId) {
                foundDataId = parent.getAttribute('data-video-id') || 
                             parent.getAttribute('data-context-item-id');
                if (foundDataId) {
                    console.log('- From parent data attribute:', foundDataId);
                }
                parent = parent.parentElement;
            }
            
            // Method 3: Nearby links
            const container = video.closest('a') || 
                            video.parentElement?.querySelector('a[href*="/watch"]') ||
                            video.parentElement?.parentElement?.querySelector('a[href*="/watch"]');
            if (container) {
                const linkVideoId = getVideoId(container.href);
                console.log('- From nearby link:', linkVideoId);
            }
        });
    }
    
    // Function to add click listeners for testing
    function addTestClickListeners() {
        console.log('\n--- Adding Test Click Listeners ---');
        
        document.querySelectorAll('video').forEach((video, index) => {
            video.addEventListener('contextmenu', function(e) {
                console.log(`\nContext menu on video ${index + 1}:`, {
                    target: e.target,
                    src: e.target.src,
                    currentSrc: e.target.currentSrc,
                    pageUrl: window.location.href
                });
            });
        });
        
        console.log('Added context menu listeners to all video elements');
    }
    
    // Run all analysis functions
    analyzeVideoElements();
    analyzeVideoDataAttributes();
    testVideoIdExtraction();
    addTestClickListeners();
    
    console.log('\n=== Debug Analysis Complete ===');
    console.log('Right-click on video elements to see context menu debug info');
    
})();
