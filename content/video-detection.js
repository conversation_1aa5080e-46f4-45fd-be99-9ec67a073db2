// Video detection utilities for YouTube content scripts
// Functions for extracting video information from YouTube DOM

/**
 * Extract video title from the page using multiple selectors
 * @returns {string} Video title or fallback
 */
function getVideoTitle() {
  // Try multiple selectors for different YouTube layouts
  const titleSelectors = [
    "h1.title.style-scope.ytd-video-primary-info-renderer",
    "h1.ytd-video-primary-info-renderer",
    'h1[class*="title"]',
    ".title.style-scope.ytd-video-primary-info-renderer",
    'meta[property="og:title"]'
  ];

  for (const selector of titleSelectors) {
    const element = document.querySelector(selector);
    if (element) {
      const title = element.textContent || element.getAttribute("content");
      if (title && title.trim()) {
        return title.trim();
      }
    }
  }

  // Fallback: try to get from document title
  if (document.title && document.title !== "YouTube") {
    const title = document.title.replace(" - YouTube", "");
    if (title.trim()) {
      return title.trim();
    }
  }

  return "Title not found";
}

/**
 * Extract video ID from various DOM elements
 * @returns {string|null} Video ID or null
 */
function getVideoIdFromDOM() {
  // Try to get video ID from URL first
  const url = window.location.href;

  // Handle Shorts URLs
  const shortsMatch = url.match(/youtube\.com\/shorts\/([^"&?\/\s]{11})/);
  if (shortsMatch && shortsMatch[1]) {
    return shortsMatch[1];
  }

  // Handle regular video URLs
  const urlMatch = url.match(
    /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/
  );
  if (urlMatch && urlMatch[1]) {
    return urlMatch[1];
  }

  // Try to get from various DOM attributes
  const videoIdSelectors = [
    "[data-video-id]",
    "[data-context-item-id]",
    'meta[itemprop="videoId"]',
    'meta[property="og:url"]'
  ];

  for (const selector of videoIdSelectors) {
    const element = document.querySelector(selector);
    if (element) {
      let videoId =
        element.getAttribute("data-video-id") ||
        element.getAttribute("data-context-item-id") ||
        element.getAttribute("content");

      if (videoId) {
        // Extract video ID from URL if needed
        const match = videoId.match(/([^"&?\/\s]{11})/);
        if (match && match[1]) {
          return match[1];
        }
      }
    }
  }

  return null;
}

/**
 * Handle click context and extract video ID
 * @param {Object} clickInfo - Click context information
 * @returns {string|null} Video ID or null
 */
function getVideoIdFromClickContext(clickInfo) {
  // First try the provided URLs
  const urls = [clickInfo.linkUrl, clickInfo.srcUrl, clickInfo.pageUrl].filter(Boolean);

  for (const url of urls) {
    // Check for Shorts first
    const shortsMatch = url.match(/youtube\.com\/shorts\/([^"&?\/\s]{11})/);
    if (shortsMatch && shortsMatch[1]) {
      return shortsMatch[1];
    }

    // Then regular videos
    const match = url.match(
      /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/
    );
    if (match && match[1]) {
      return match[1];
    }
  }

  // Fallback to DOM extraction
  return getVideoIdFromDOM();
}

/**
 * Get title for a specific video ID by looking at links and containers
 * @param {string} videoId - Video ID to search for
 * @returns {string} Video title or fallback
 */
function getVideoTitleForId(videoId) {
  if (!videoId) return "Title not found";

  // First check if we're on the video's page
  const currentUrl = window.location.href;
  if (currentUrl.includes(`/watch?v=${videoId}`)) {
    return getVideoTitle();
  }

  // Look for any element that references this video ID
  const videoIdSelectors = [
    `[href*="/watch?v=${videoId}"]`,
    `[href*="/shorts/${videoId}"]`,
    `[data-video-id="${videoId}"]`,
    `[data-context-item-id="${videoId}"]`
  ];

  for (const selector of videoIdSelectors) {
    const elements = document.querySelectorAll(selector);

    for (const element of elements) {
      // Find the container that holds both the link and the title
      const containers = [
        element.closest("ytd-rich-item-renderer"),
        element.closest("ytd-video-renderer"),
        element.closest("ytd-compact-video-renderer"),
        element.closest("ytd-grid-video-renderer"),
        element.closest("ytd-playlist-video-renderer"),
        element.closest("ytd-reel-item-renderer"), // YouTube Shorts
        element.closest("ytd-shorts-lockup-view-model"), // YouTube Shorts
        element.closest("#dismissible"),
        element.closest(".style-scope.ytd-item-section-renderer")
      ].filter(Boolean);

      for (const container of containers) {
        const title = extractTitleFromContainer(container, videoId);
        if (title) return title;
      }

      // Direct checks on the element itself
      if (element.title && element.title.trim()) {
        return element.title.trim();
      }

      const ariaLabel = element.getAttribute("aria-label");
      if (ariaLabel && ariaLabel.trim()) {
        return ariaLabel.trim();
      }
    }
  }

  return "Title not found";
}

/**
 * Extract title from container element
 * @param {Element} container - Container element
 * @param {string} videoId - Video ID for validation
 * @returns {string|null} Title or null
 */
function extractTitleFromContainer(container, videoId) {
  // Look for title in various places within the container
  const titleSelectors = [
    "#video-title",
    "#video-title-text",
    "h3 #video-title",
    "h3 a#video-title-link",
    'a[id="video-title"]',
    'span[id="video-title"]',
    ".title",
    "h3.ytd-video-renderer",
    'yt-formatted-string[class*="video-title"]'
  ];

  for (const titleSelector of titleSelectors) {
    const titleElement = container.querySelector(titleSelector);
    if (titleElement) {
      // Get text content, handling both regular elements and yt-formatted-string
      const titleText = (
        titleElement.textContent ||
        titleElement.innerText ||
        ""
      ).trim();
      if (titleText && titleText.length > 0) {
        return titleText;
      }
    }
  }

  // Also check aria-label on links within container (but only video links)
  const videoLinks = container.querySelectorAll(
    `a[href*="/watch?v=${videoId}"], a[href*="/shorts/${videoId}"]`
  );
  for (const link of videoLinks) {
    const ariaLabel = link.getAttribute("aria-label");
    if (ariaLabel && ariaLabel.trim() && !ariaLabel.includes("SHIFT+")) {
      return ariaLabel.trim();
    }
  }

  return null;
}

/**
 * Extract video duration from thumbnail overlay
 * @param {string} videoId - Video ID
 * @returns {string|null} Duration or null
 */
function getVideoDuration(videoId) {
  if (!videoId) return null;

  // Try to find duration in current video page first
  const currentUrl = window.location.href;
  if (currentUrl.includes(`/watch?v=${videoId}`)) {
    // On video page, try to get duration from video element or page metadata
    const durationSelectors = [
      '.ytp-time-duration',
      'meta[itemprop="duration"]',
      '.ytd-thumbnail-overlay-time-status-renderer .badge-shape__text'
    ];
    
    for (const selector of durationSelectors) {
      const element = document.querySelector(selector);
      if (element) {
        const duration = element.textContent || element.getAttribute('content');
        if (duration && duration.trim()) {
          return duration.trim();
        }
      }
    }
  }

  return findMetadataInThumbnails(videoId, 'duration');
}

/**
 * Extract channel name for video
 * @param {string} videoId - Video ID
 * @returns {string|null} Channel name or null
 */
function getChannelName(videoId) {
  if (!videoId) return null;

  // Try to find channel in current video page first
  const currentUrl = window.location.href;
  if (currentUrl.includes(`/watch?v=${videoId}`)) {
    const channelSelectors = [
      '.ytd-channel-name a',
      '.ytd-video-owner-renderer a',
      'meta[itemprop="author"]',
      '.owner-name a'
    ];
    
    for (const selector of channelSelectors) {
      const element = document.querySelector(selector);
      if (element) {
        const channel = element.textContent || element.getAttribute('content');
        if (channel && channel.trim()) {
          return channel.trim();
        }
      }
    }
  }

  return findMetadataInThumbnails(videoId, 'channel');
}

/**
 * Extract view count for video
 * @param {string} videoId - Video ID
 * @returns {string|null} View count or null
 */
function getViewCount(videoId) {
  if (!videoId) return null;

  // Try to find views in current video page first
  const currentUrl = window.location.href;
  if (currentUrl.includes(`/watch?v=${videoId}`)) {
    const viewSelectors = [
      '.view-count',
      '.ytd-video-view-count-renderer',
      'meta[itemprop="interactionCount"]'
    ];
    
    for (const selector of viewSelectors) {
      const element = document.querySelector(selector);
      if (element) {
        const views = element.textContent || element.getAttribute('content');
        if (views && views.includes('views')) {
          return views.trim();
        }
      }
    }
  }

  return findMetadataInThumbnails(videoId, 'views');
}

/**
 * Extract upload time for video
 * @param {string} videoId - Video ID
 * @returns {string|null} Upload time or null
 */
function getUploadTime(videoId) {
  if (!videoId) return null;

  // Try to find upload time in current video page first
  const currentUrl = window.location.href;
  if (currentUrl.includes(`/watch?v=${videoId}`)) {
    const timeSelectors = [
      '.ytd-video-primary-info-renderer .date',
      'meta[itemprop="uploadDate"]',
      '.upload-date'
    ];
    
    for (const selector of timeSelectors) {
      const element = document.querySelector(selector);
      if (element) {
        const time = element.textContent || element.getAttribute('content');
        if (time && (time.includes('ago') || time.includes('hour') || time.includes('day'))) {
          return time.trim();
        }
      }
    }
  }

  return findMetadataInThumbnails(videoId, 'time');
}

/**
 * Find metadata in video thumbnail cards
 * @param {string} videoId - Video ID
 * @param {string} type - Type of metadata (duration, channel, views, time)
 * @returns {string|null} Metadata value or null
 */
function findMetadataInThumbnails(videoId, type) {
  const videoIdSelectors = [
    `[href*="/watch?v=${videoId}"]`,
    `[href*="/shorts/${videoId}"]`,
    `[data-video-id="${videoId}"]`
  ];

  for (const selector of videoIdSelectors) {
    const elements = document.querySelectorAll(selector);
    
    for (const element of elements) {
      const container = element.closest('ytd-rich-item-renderer') || 
                      element.closest('ytd-video-renderer') ||
                      element.closest('ytd-compact-video-renderer') ||
                      element.closest('.yt-lockup-view-model-wiz') ||
                      element.closest('#dismissible');
                      
      if (container) {
        const metadata = extractMetadataFromContainer(container, type);
        if (metadata) return metadata;
      }
    }
  }

  return null;
}

/**
 * Extract specific metadata from container
 * @param {Element} container - Container element
 * @param {string} type - Metadata type
 * @returns {string|null} Metadata value or null
 */
function extractMetadataFromContainer(container, type) {
  switch (type) {
    case 'duration':
      return extractDurationFromContainer(container);
    case 'channel':
      return extractChannelFromContainer(container);
    case 'views':
      return extractViewsFromContainer(container);
    case 'time':
      return extractTimeFromContainer(container);
    default:
      return null;
  }
}

/**
 * Extract duration from container
 * @param {Element} container - Container element
 * @returns {string|null} Duration or null
 */
function extractDurationFromContainer(container) {
  const durationSelectors = [
    '.badge-shape-wiz__text',
    '.badge-shape__text',
    '.ytd-thumbnail-overlay-time-status-renderer .badge-shape__text',
    '.yt-thumbnail-overlay-badge-view-model-wiz .badge-shape-wiz__text'
  ];
  
  for (const durationSelector of durationSelectors) {
    const durationElement = container.querySelector(durationSelector);
    if (durationElement) {
      const duration = durationElement.textContent?.trim();
      // Check if it looks like a duration (contains : and numbers)
      if (duration && /^\d+:\d+$|^\d+:\d+:\d+$/.test(duration)) {
        return duration;
      }
    }
  }
  return null;
}

/**
 * Extract channel from container
 * @param {Element} container - Container element
 * @returns {string|null} Channel name or null
 */
function extractChannelFromContainer(container) {
  const channelSelectors = [
    '.yt-content-metadata-view-model-wiz__metadata-text:first-child',
    '.ytd-channel-name a',
    '.yt-simple-endpoint.style-scope.yt-formatted-string',
    '.channel-name'
  ];
  
  for (const channelSelector of channelSelectors) {
    const channelElement = container.querySelector(channelSelector);
    if (channelElement) {
      const channelText = channelElement.textContent?.trim();
      // Make sure it's not view count or other metadata
      if (channelText && !channelText.includes('views') && !channelText.includes('ago') && !channelText.includes('•')) {
        return channelText;
      }
    }
  }
  return null;
}

/**
 * Extract views from container
 * @param {Element} container - Container element
 * @returns {string|null} View count or null
 */
function extractViewsFromContainer(container) {
  const metadataTexts = container.querySelectorAll('.yt-content-metadata-view-model-wiz__metadata-text');
  for (const textElement of metadataTexts) {
    const text = textElement.textContent?.trim();
    if (text && text.includes('views')) {
      return text;
    }
  }
  return null;
}

/**
 * Extract time from container
 * @param {Element} container - Container element
 * @returns {string|null} Upload time or null
 */
function extractTimeFromContainer(container) {
  const metadataTexts = container.querySelectorAll('.yt-content-metadata-view-model-wiz__metadata-text');
  for (const textElement of metadataTexts) {
    const text = textElement.textContent?.trim();
    if (text && (text.includes('ago') || text.includes('hour') || text.includes('day') || 
        text.includes('week') || text.includes('month') || text.includes('year'))) {
      return text;
    }
  }
  return null;
}

/**
 * Get comprehensive video metadata
 * @param {string} videoId - Video ID
 * @returns {Object} Metadata object
 */
function getVideoMetadata(videoId) {
  if (!videoId) return null;

  return {
    duration: getVideoDuration(videoId),
    channelName: getChannelName(videoId),
    viewCount: getViewCount(videoId),
    uploadTime: getUploadTime(videoId)
  };
}

// Export for both CommonJS (tests) and global scope (extension)
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    getVideoTitle,
    getVideoIdFromDOM,
    getVideoIdFromClickContext,
    getVideoTitleForId,
    getVideoDuration,
    getChannelName,
    getViewCount,
    getUploadTime,
    getVideoMetadata,
    extractTitleFromContainer,
    findMetadataInThumbnails,
    extractMetadataFromContainer
  };
} else {
  // For browser extension context
  window.VideoDetection = {
    getVideoTitle,
    getVideoIdFromDOM,
    getVideoIdFromClickContext,
    getVideoTitleForId,
    getVideoDuration,
    getChannelName,
    getViewCount,
    getUploadTime,
    getVideoMetadata,
    extractTitleFromContainer,
    findMetadataInThumbnails,
    extractMetadataFromContainer
  };
}