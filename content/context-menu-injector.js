// Alternative approach: Inject custom context menu handling for video thumbnails
// This script actively monitors for right-clicks on video elements and provides fallback functionality

(function() {
    'use strict';
    
    // Debug logging
    function injectorLog(message, data = null) {
        // Temporarily disabled to reduce console noise
        // const timestamp = new Date().toISOString();
        // console.log(`[YT-Summarizer-Injector ${timestamp}] ${message}`, data || '');
    }
    
    // Extract video ID from URL
    function extractVideoIdFromUrl(url) {
        if (!url) return null;
        const youtubeRegex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=|shorts\/)|youtu\.be\/)([^"&?\/\s]{11})/;
        const match = url.match(youtubeRegex);
        return (match && match[1]) ? match[1] : null;
    }
    
    // Find video ID from element and its context
    function findVideoIdFromElement(element) {
        injectorLog("Searching for video ID from element", element.tagName);
        
        // Method 1: Check data attributes on the element and its parents
        let current = element;
        let level = 0;
        while (current && level < 10) {
            const dataVideoId = current.getAttribute('data-video-id') || 
                               current.getAttribute('data-context-item-id');
            if (dataVideoId && dataVideoId.length === 11) {
                injectorLog(`Found video ID in data attribute at level ${level}`, dataVideoId);
                return dataVideoId;
            }
            
            // Check if current element is a link
            if (current.tagName === 'A' && current.href) {
                const videoId = extractVideoIdFromUrl(current.href);
                if (videoId) {
                    injectorLog(`Found video ID in link at level ${level}`, videoId);
                    return videoId;
                }
            }
            
            current = current.parentElement;
            level++;
        }
        
        // Method 2: Look for nearby links
        const container = element.closest('.ytd-rich-item-renderer, .ytd-video-renderer, .ytd-compact-video-renderer, ytd-thumbnail') ||
                         element.parentElement;
        
        if (container) {
            const links = container.querySelectorAll('a[href*="/watch"], a[href*="youtu.be"], a[href*="/shorts"]');
            for (const link of links) {
                const videoId = extractVideoIdFromUrl(link.href);
                if (videoId) {
                    injectorLog("Found video ID in container link", videoId);
                    return videoId;
                }
            }
        }
        
        // Method 3: Check for video elements and their sources
        if (element.tagName === 'VIDEO') {
            // Look for the thumbnail container this video belongs to
            const thumbnailContainer = element.closest('ytd-thumbnail, .ytd-thumbnail');
            if (thumbnailContainer) {
                const containerLink = thumbnailContainer.querySelector('a') || thumbnailContainer.closest('a');
                if (containerLink && containerLink.href) {
                    const videoId = extractVideoIdFromUrl(containerLink.href);
                    if (videoId) {
                        injectorLog("Found video ID from thumbnail container link", videoId);
                        return videoId;
                    }
                }
            }
        }
        
        injectorLog("No video ID found from element");
        return null;
    }
    
    // Track the last right-clicked element
    let lastRightClickedElement = null;
    let lastRightClickTime = 0;
    
    // Add context menu event listener
    document.addEventListener('contextmenu', function(event) {
        injectorLog("Context menu event detected", {
            target: event.target.tagName,
            className: event.target.className,
            id: event.target.id
        });
        
        lastRightClickedElement = event.target;
        lastRightClickTime = Date.now();
        
        // Check if this might be a video thumbnail
        const isVideoElement = event.target.tagName === 'VIDEO';
        const isInVideoContainer = event.target.closest('.ytd-rich-item-renderer, .ytd-video-renderer, .ytd-compact-video-renderer, ytd-thumbnail');
        
        if (isVideoElement || isInVideoContainer) {
            const videoId = findVideoIdFromElement(event.target);
            injectorLog("Right-click on potential video element", {
                isVideoElement,
                isInVideoContainer,
                videoId,
                element: event.target
            });
            
            // Store this information for the background script to potentially use
            if (videoId) {
                // We could potentially show a custom context menu here
                // or store the video ID for the background script to pick up
                window.lastDetectedVideoId = videoId;
                window.lastDetectedVideoTime = Date.now();
                
                injectorLog("Stored video ID for potential use", videoId);
            }
        }
    }, true); // Use capture phase to catch events early
    
    // Listen for messages from background script asking for the last detected video
    browser.runtime.onMessage.addListener((message, sender, sendResponse) => {
        if (message.action === "getLastDetectedVideo") {
            const timeDiff = Date.now() - (window.lastDetectedVideoTime || 0);
            
            // Only return the video ID if it was detected recently (within 5 seconds)
            if (window.lastDetectedVideoId && timeDiff < 5000) {
                injectorLog("Returning last detected video ID", {
                    videoId: window.lastDetectedVideoId,
                    timeDiff
                });
                return Promise.resolve(window.lastDetectedVideoId);
            } else {
                injectorLog("No recent video ID detected", { timeDiff });
                return Promise.resolve(null);
            }
        }
        
        return true;
    });
    
    // Alternative approach: Monitor for video thumbnail hover states
    let hoveredVideoId = null;
    
    document.addEventListener('mouseover', function(event) {
        const videoContainer = event.target.closest('.ytd-rich-item-renderer, .ytd-video-renderer, .ytd-compact-video-renderer');
        if (videoContainer) {
            const videoId = findVideoIdFromElement(event.target);
            if (videoId) {
                hoveredVideoId = videoId;
                window.lastHoveredVideoId = videoId;
                window.lastHoveredVideoTime = Date.now();
            }
        }
    });
    
    injectorLog("Context menu injector initialized");
    
})();
