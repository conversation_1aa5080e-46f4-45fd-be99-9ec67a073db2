// Simple content script for YouTube video title extraction and video ID detection
// This file imports video detection utilities and handles message routing

// Import video detection functions if available
if (typeof VideoDetection === 'undefined') {
  // Load video detection module - in extension context this would be loaded by manifest
  console.log('[YT-Bookmarker] VideoDetection module not loaded, using fallback functions');
}

// Wrapper functions that use VideoDetection module or fallback implementations
function getVideoTitle() {
  if (typeof VideoDetection !== 'undefined') {
    return VideoDetection.getVideoTitle();
  }
  // Fallback implementation would go here
  return "Title not found";
}

function getVideoIdFromDOM() {
  if (typeof VideoDetection !== 'undefined') {
    return VideoDetection.getVideoIdFromDOM();
  }
  // Fallback implementation would go here
  return null;
}

function getVideoIdFromClickContext(clickInfo) {
  if (typeof VideoDetection !== 'undefined') {
    return VideoDetection.getVideoIdFromClickContext(clickInfo);
  }
  // Fallback implementation
  return null;
}

function getVideoTitleForId(videoId) {
  if (typeof VideoDetection !== 'undefined') {
    return VideoDetection.getVideoTitleForId(videoId);
  }
  return "Title not found";
}

function getVideoMetadata(videoId) {
  if (typeof VideoDetection !== 'undefined') {
    return VideoDetection.getVideoMetadata(videoId);
  }
  return null;
}

// Listen for messages from background script
browser.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === "getVideoTitle") {
    const title = getVideoTitle();
    sendResponse(title);
    return true;
  }

  if (request.action === "getVideoTitleForId") {
    const title = getVideoTitleForId(request.videoId);
    sendResponse(title);
    return true;
  }

  if (request.action === "getVideoIdFromClickContext") {
    const videoId = getVideoIdFromClickContext(request.clickInfo);
    sendResponse(videoId);
    return true;
  }

  if (request.action === "getVideoMetadata") {
    const metadata = getVideoMetadata(request.videoId);
    sendResponse(metadata);
    return true;
  }

  return false;
});
