{"name": "youtube-video-bookmarker", "version": "1.0.0", "description": "Firefox extension to bookmark YouTube videos with transcript extraction", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "web-ext lint", "build": "web-ext build", "dev": "web-ext run"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-transform-optional-chaining": "^7.27.1", "@babel/preset-env": "^7.28.0", "babel-jest": "^30.0.5", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-webextension-mock": "^3.8.9", "nunjucks": "^3.2.4", "sinon": "^17.0.1"}}