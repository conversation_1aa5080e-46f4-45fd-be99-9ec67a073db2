{"manifest_version": 2, "name": "YouTube Video Bookmarker", "version": "1.0.0", "description": "Bookmark YouTube videos for later viewing.", "permissions": ["storage", "contextMenus", "activeTab", "tabs", "*://*.youtube.com/*", "https://api.apify.com/*", "https://api.openai.com/*"], "content_security_policy": "script-src 'self' 'unsafe-eval'; object-src 'self'", "background": {"scripts": ["lib/nunjucks-slim.min.js", "lib/templates-precompiled.js", "lib/system-prompts.js", "lib/video-processor.js", "lib/browser-apis.js", "services/nunjucks-service.js", "services/openai-service.js", "services/openai-video-analyzer.js", "services/robust-openai-service.js", "services/robust-video-analyzer.js", "services/transcript-cleaner-service.js", "services/title-response-service.js", "services/investigative-reporter-service.js", "background/storage.js", "background/context-menu.js", "background/apify-transcript.js", "background/background.js"], "persistent": true}, "content_scripts": [{"matches": ["*://*.youtube.com/*"], "js": ["content/video-detection.js", "content/content.js", "content/context-menu-injector.js"]}], "icons": {"48": "icons/summarizer-48.png"}, "browser_action": {"default_icon": "icons/summarizer-48.png", "default_title": "YouTube Bookmarker", "default_popup": "ui/panel.html"}}