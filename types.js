// OpenAI Chat Completion API Type Definitions

class Message {
  constructor(role, content) {
    this.role = role; // 'system' | 'user' | 'assistant'
    this.content = content;
    this.refusal = null;
    this.annotations = [];
  }

  static system(content) {
    return new Message('system', content);
  }

  static user(content) {
    return new Message('user', content);
  }

  static assistant(content) {
    return new Message('assistant', content);
  }
}

class ToolCall {
  constructor(id, type, functionCall) {
    this.id = id;
    this.type = type; // Currently only 'function'
    this.function = functionCall;
  }
}

class FunctionCall {
  constructor(name, args) {
    this.name = name;
    this.arguments = args; // JSON string
  }
}

class TokenLogProb {
  constructor(token, logprob, bytes = null, topLogprobs = []) {
    this.token = token;
    this.logprob = logprob;
    this.bytes = bytes;
    this.top_logprobs = topLogprobs;
  }
}

class LogProbs {
  constructor(content = null, refusal = null) {
    this.content = content; // array of TokenLogProb or null
    this.refusal = refusal; // array of TokenLogProb or null
  }
}

class Choice {
  constructor(index, message, finishReason, logprobs = null) {
    this.index = index;
    this.message = message; // Message object
    this.finish_reason = finishReason; // 'stop' | 'length' | 'content_filter' | 'tool_calls' | 'function_call'
    this.logprobs = logprobs; // LogProbs object or null
  }
}

class PromptTokensDetails {
  constructor(audioTokens = 0, cachedTokens = 0) {
    this.audio_tokens = audioTokens;
    this.cached_tokens = cachedTokens;
  }
}

class CompletionTokensDetails {
  constructor(reasoningTokens = 0, audioTokens = 0, acceptedPredictionTokens = 0, rejectedPredictionTokens = 0) {
    this.reasoning_tokens = reasoningTokens;
    this.audio_tokens = audioTokens;
    this.accepted_prediction_tokens = acceptedPredictionTokens;
    this.rejected_prediction_tokens = rejectedPredictionTokens;
  }
}

class Usage {
  constructor(promptTokens, completionTokens, totalTokens, promptTokensDetails = null, completionTokensDetails = null) {
    this.prompt_tokens = promptTokens;
    this.completion_tokens = completionTokens;
    this.total_tokens = totalTokens;
    this.prompt_tokens_details = promptTokensDetails || new PromptTokensDetails();
    this.completion_tokens_details = completionTokensDetails || new CompletionTokensDetails();
  }
}

class CompletionResponse {
  constructor(id, object, created, model, choices, usage, serviceTier = null, systemFingerprint = null) {
    this.id = id;
    this.object = object; // 'chat.completion'
    this.created = created; // Unix timestamp
    this.model = model;
    this.choices = choices; // Array of Choice objects
    this.usage = usage; // Usage object
    this.service_tier = serviceTier;
    this.system_fingerprint = systemFingerprint;
  }

  getContent() {
    return this.choices[0]?.message?.content || '';
  }

  getFinishReason() {
    return this.choices[0]?.finish_reason || '';
  }

  getTotalTokens() {
    return this.usage?.total_tokens || 0;
  }

  getPromptTokens() {
    return this.usage?.prompt_tokens || 0;
  }

  getCompletionTokens() {
    return this.usage?.completion_tokens || 0;
  }

  getUsage() {
    return this.usage;
  }

  hasToolCalls() {
    return this.choices.length > 0 && this.choices[0].message?.tool_calls?.length > 0;
  }

  getToolCalls() {
    if (!this.hasToolCalls()) {
      return [];
    }
    return this.choices[0].message.tool_calls;
  }

  getFirstToolCall() {
    const toolCalls = this.getToolCalls();
    return toolCalls.length > 0 ? toolCalls[0] : null;
  }

  getMessage() {
    return this.choices[0]?.message;
  }

  parseJSONContent() {
    const content = this.getContent();
    if (!content) {
      throw new Error("No content available for JSON parsing");
    }
    
    try {
      // Clean the content by removing markdown code blocks if present
      let cleanContent = content.trim();
      
      // Remove markdown JSON code blocks
      if (cleanContent.startsWith('```json') && cleanContent.endsWith('```')) {
        cleanContent = cleanContent.slice(7, -3).trim();
      } else if (cleanContent.startsWith('```') && cleanContent.endsWith('```')) {
        cleanContent = cleanContent.slice(3, -3).trim();
      }
      
      return JSON.parse(cleanContent);
    } catch (error) {
      // If parsing fails, try to extract JSON from the response
      try {
        const jsonMatch = content.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          return JSON.parse(jsonMatch[0]);
        }
      } catch (secondError) {
        // If all else fails, throw the original error with context
        throw new Error(`Failed to parse JSON content: ${error.message}. Content: ${content.substring(0, 200)}...`);
      }
      
      throw new Error(`Failed to parse JSON content: ${error.message}. Content: ${content.substring(0, 200)}...`);
    }
  }
}

class CompletionOptions {
  constructor({
    model = 'gpt-4.1-nano',
    temperature = 0.7,
    max_tokens = 150,
    top_p = 1,
    frequency_penalty = 0,
    presence_penalty = 0,
    stop = null,
    n = 1,
    stream = false,
    logprobs = false,
    top_logprobs = null,
    service_tier = null,
    seed = null
  } = {}) {
    this.model = model;
    this.temperature = temperature;
    this.max_tokens = max_tokens;
    this.top_p = top_p;
    this.frequency_penalty = frequency_penalty;
    this.presence_penalty = presence_penalty;
    this.stop = stop;
    this.n = n;
    this.stream = stream;
    this.logprobs = logprobs;
    this.top_logprobs = top_logprobs;
    this.service_tier = service_tier;
    this.seed = seed;
  }
}

class ServiceConfig {
  constructor({
    apiKey = null,
    baseUrl = 'https://api.openai.com/v1',
    defaultModel = 'gpt-4.1-nano',
    timeout = 30000,
    maxRetries = 3
  } = {}) {
    this.apiKey = apiKey;
    this.baseUrl = baseUrl;
    this.defaultModel = defaultModel;
    this.timeout = timeout;
    this.maxRetries = maxRetries;
  }
}

// Custom Error Classes
class OpenAIError extends Error {
  constructor(message, type = 'unknown', statusCode = null) {
    super(message);
    this.name = 'OpenAIError';
    this.type = type;
    this.statusCode = statusCode;
  }
}

class ValidationError extends OpenAIError {
  constructor(message) {
    super(message, 'validation');
    this.name = 'ValidationError';
  }
}

class APIError extends OpenAIError {
  constructor(message, statusCode) {
    super(message, 'api', statusCode);
    this.name = 'APIError';
  }
}

class NetworkError extends OpenAIError {
  constructor(message) {
    super(message, 'network');
    this.name = 'NetworkError';
  }
}

// Export for Node.js modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    Message,
    ToolCall,
    FunctionCall,
    TokenLogProb,
    LogProbs,
    Choice,
    PromptTokensDetails,
    CompletionTokensDetails,
    Usage,
    CompletionResponse,
    CompletionOptions,
    ServiceConfig,
    OpenAIError,
    ValidationError,
    APIError,
    NetworkError
  };
}