{"permissions": {"allow": ["Bash(node:*)", "Bash(rg:*)", "Bash(grep:*)", "Bash(rm:*)", "WebFetch(domain:platform.openai.com)", "Bash(find:*)", "Bash(web-ext build:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./test-gemini-tts.sh:*)", "Bash(bash:*)", "<PERSON><PERSON>(cat:*)", "Bash(ffmpeg:*)", "mcp__ide__executeCode", "mcp__ide__getDiagnostics", "Bash(npx web-ext lint:*)", "Bash(npx web-ext build:*)", "Bash(web-ext run:*)", "Bash(npm run lint)", "Bash(npm test)", "Bash(npm install:*)", "<PERSON><PERSON>(curl:*)", "Bash(ls:*)"], "deny": []}}