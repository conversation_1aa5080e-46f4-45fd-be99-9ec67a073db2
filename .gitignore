# Node.js dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.production

# API keys and secrets
config/secrets.json
secrets.json
api-keys.json

# Build artifacts
web-ext-artifacts/
dist/
build/

# Test coverage
coverage/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp
.cache/

# Firefox extension development
web-ext-config.js

# Babel
.babelrc.js

# Jest
jest.config.local.js

# Debug files (optional - uncomment if you don't want to track these)
# debug/
# test/

# Documentation artifacts (optional)
# docs/generated/