module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/test/setup.js'],
  
  // Enable ES6 modules support
  transform: {
    '^.+\.js$': 'babel-jest'
  },
  transformIgnorePatterns: [
    'node_modules/(?!(jest-webextension-mock)/)'  
  ],
  
  // Coverage collection
  collectCoverageFrom: [
    'background/**/*.js',
    'content/**/*.js', 
    'lib/**/*.js',
    'browser-*.js',
    '!test/**',
    '!debug/**',
    '!web-ext-artifacts/**',
    '!node_modules/**',
    '!coverage/**'
  ],
  
  // Test file patterns
  testMatch: [
    '<rootDir>/test/**/*.test.js'
  ],
  
  // Coverage thresholds
  coverageThreshold: {
    global: {
      branches: 50,
      functions: 50,
      lines: 50,
      statements: 50
    }
  },
  
  // Module name mapping for cleaner imports
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/$1'
  },
  
  // Verbose output for better debugging
  verbose: true
};