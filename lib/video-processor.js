// Shared video processing utilities
// Common functions for video ID extraction, validation, and processing

/**
 * Extract YouTube video ID from various URL formats
 * @param {string} url - YouTube URL
 * @returns {string|null} - Video ID or null if not found
 */
function getVideoId(url) {
  if (!url) return null;

  // Handle YouTube Shorts URLs first
  const shortsMatch = url.match(/youtube\.com\/shorts\/([^"&?\/\s]{11})/);
  if (shortsMatch && shortsMatch[1]) {
    return shortsMatch[1];
  }

  // Regular YouTube video regex
  const youtubeRegex =
    /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
  const match = url.match(youtubeRegex);
  return match && match[1] ? match[1] : null;
}

/**
 * Validate if a string is a valid YouTube video ID
 * @param {string} videoId - Potential video ID
 * @returns {boolean} - True if valid
 */
function isValidVideoId(videoId) {
  if (!videoId || typeof videoId !== 'string') return false;
  return /^[a-zA-Z0-9_-]{11}$/.test(videoId);
}

/**
 * Generate YouTube video URL from video ID
 * @param {string} videoId - YouTube video ID
 * @returns {string} - Full YouTube URL
 */
function generateVideoUrl(videoId) {
  if (!isValidVideoId(videoId)) {
    throw new Error('Invalid video ID');
  }
  return `https://www.youtube.com/watch?v=${videoId}`;
}

/**
 * Generate YouTube thumbnail URL
 * @param {string} videoId - YouTube video ID
 * @param {string} quality - Thumbnail quality (default, mqdefault, hqdefault, maxresdefault)
 * @returns {string} - Thumbnail URL
 */
function generateThumbnailUrl(videoId, quality = 'mqdefault') {
  if (!isValidVideoId(videoId)) {
    throw new Error('Invalid video ID');
  }
  return `https://i.ytimg.com/vi/${videoId}/${quality}.jpg`;
}

/**
 * Create a new video object with default properties
 * @param {string} videoId - YouTube video ID
 * @param {string} title - Video title
 * @param {Object} metadata - Additional metadata
 * @returns {Object} - Video object
 */
function createVideoObject(videoId, title = null, metadata = {}) {
  if (!isValidVideoId(videoId)) {
    throw new Error('Invalid video ID');
  }

  return {
    id: videoId,
    url: generateVideoUrl(videoId),
    title: title || `YouTube Video - ${videoId}`,
    thumbnailUrl: generateThumbnailUrl(videoId),
    dateAdded: Date.now(),
    transcript: null,
    article: null,
    transcriptStatus: 'pending',
    duration: metadata.duration || null,
    channelName: metadata.channelName || null,
    viewCount: metadata.viewCount || null,
    uploadTime: metadata.uploadTime || null,
    audioData: null,
    hasAudio: false
  };
}

/**
 * Extract video ID from context info (for context menu handling)
 * @param {Object} contextInfo - Context menu info object
 * @returns {string|null} - Video ID or null
 */
function extractVideoIdFromContext(contextInfo) {
  if (!contextInfo) return null;

  // Try all possible URLs from context
  const possibleUrls = [
    contextInfo.linkUrl,
    contextInfo.srcUrl,
    contextInfo.pageUrl
  ].filter(Boolean);

  for (const url of possibleUrls) {
    const videoId = getVideoId(url);
    if (videoId) {
      return videoId;
    }
  }

  return null;
}

/**
 * Check if video exists in video list
 * @param {Array} videos - Array of video objects
 * @param {string} videoId - Video ID to check
 * @returns {boolean} - True if video exists
 */
function videoExists(videos, videoId) {
  if (!Array.isArray(videos) || !videoId) return false;
  return videos.some(video => video.id === videoId);
}

/**
 * Find video by ID in video list
 * @param {Array} videos - Array of video objects
 * @param {string} videoId - Video ID to find
 * @returns {Object|null} - Video object or null
 */
function findVideoById(videos, videoId) {
  if (!Array.isArray(videos) || !videoId) return null;
  return videos.find(video => video.id === videoId) || null;
}

/**
 * Update video status in video list
 * @param {Array} videos - Array of video objects
 * @param {string} videoId - Video ID to update
 * @param {Object} updates - Updates to apply
 * @returns {Array} - Updated video array
 */
function updateVideoInList(videos, videoId, updates) {
  if (!Array.isArray(videos) || !videoId) return videos;
  
  return videos.map(video => 
    video.id === videoId ? { ...video, ...updates } : video
  );
}

/**
 * Remove video from video list
 * @param {Array} videos - Array of video objects
 * @param {string} videoId - Video ID to remove
 * @returns {Array} - Updated video array
 */
function removeVideoFromList(videos, videoId) {
  if (!Array.isArray(videos) || !videoId) return videos;
  return videos.filter(video => video.id !== videoId);
}

// Export for both CommonJS (tests) and global scope (extension)
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    getVideoId,
    isValidVideoId,
    generateVideoUrl,
    generateThumbnailUrl,
    createVideoObject,
    extractVideoIdFromContext,
    videoExists,
    findVideoById,
    updateVideoInList,
    removeVideoFromList
  };
} else {
  // For browser extension context
  window.VideoProcessor = {
    getVideoId,
    isValidVideoId,
    generateVideoUrl,
    generateThumbnailUrl,
    createVideoObject,
    extractVideoIdFromContext,
    videoExists,
    findVideoById,
    updateVideoInList,
    removeVideoFromList
  };
}