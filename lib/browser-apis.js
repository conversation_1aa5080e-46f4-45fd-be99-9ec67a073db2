// Browser API abstractions for testing compatibility
// This module provides a unified interface for browser APIs that works in both
// extension context and test environment

class BrowserAPIs {
  constructor() {
    // Use actual browser APIs in extension context, mocks in test context
    this.storage = typeof browser !== 'undefined' ? browser.storage : null;
    this.runtime = typeof browser !== 'undefined' ? browser.runtime : null;
    this.contextMenus = typeof browser !== 'undefined' ? browser.contextMenus : null;
    this.tabs = typeof browser !== 'undefined' ? browser.tabs : null;
  }

  // Storage API wrapper
  async getStorageLocal(keys) {
    if (this.storage) {
      return await this.storage.local.get(keys);
    }
    // Mock implementation for tests
    throw new Error('Storage API not available');
  }

  async setStorageLocal(data) {
    if (this.storage) {
      return await this.storage.local.set(data);
    }
    // Mock implementation for tests
    throw new Error('Storage API not available');
  }

  // Context Menu API wrapper
  createContextMenu(menuConfig, callback) {
    if (this.contextMenus) {
      return this.contextMenus.create(menuConfig, callback);
    }
    // Mock implementation for tests
    throw new Error('Context menu API not available');
  }

  // Runtime API wrapper
  getURL(path) {
    if (this.runtime) {
      return this.runtime.getURL(path);
    }
    // Mock implementation for tests
    return `moz-extension://mock/${path}`;
  }

  // Tabs API wrapper
  async createTab(options) {
    if (this.tabs) {
      return await this.tabs.create(options);
    }
    // Mock implementation for tests
    throw new Error('Tabs API not available');
  }

  async queryTabs(options) {
    if (this.tabs) {
      return await this.tabs.query(options);
    }
    // Mock implementation for tests
    return [];
  }

  async updateTab(tabId, options) {
    if (this.tabs) {
      return await this.tabs.update(tabId, options);
    }
    // Mock implementation for tests
    throw new Error('Tabs API not available');
  }

  async executeScript(tabId, details) {
    if (this.tabs) {
      return await this.tabs.executeScript(tabId, details);
    }
    // Mock implementation for tests
    throw new Error('Script execution not available');
  }

  async sendMessage(tabId, message) {
    if (this.tabs) {
      return await this.tabs.sendMessage(tabId, message);
    }
    // Mock implementation for tests
    throw new Error('Message sending not available');
  }
}

// Create singleton instance
const browserAPIs = new BrowserAPIs();

// Export for both CommonJS (tests) and global scope (extension)
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { BrowserAPIs, browserAPIs };
} else {
  // For browser extension context
  window.BrowserAPIs = BrowserAPIs;
  window.browserAPIs = browserAPIs;
}