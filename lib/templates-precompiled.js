(function() {(window.nunjucksPrecompiled = window.nunjucksPrecompiled || {})["investigative-reporter.njk"] = (function() {
function root(env, context, frame, runtime, cb) {
var lineno = 0;
var colno = 0;
var output = "";
try {
var parentTemplate = null;
output += "Transform this YouTube transcript into a news story that completes a clickbait title, then provides dense technical coverage.\r\n\r\nRequirements:\r\n- Write the first sentence assuming a clickbait title was just read - it should complete the thought naturally\r\n- First sentence (20-40 words) delivers the real story with key limitation/reality check\r\n- Follow with dense technical narrative (100-150 words) covering implementation, benchmarks, comparisons\r\n- Include EVERY number, specification, and technical detail from transcript\r\n- Use specific comparisons (IBM's 2ms vs their 10ms)\r\n- Must sound natural for TV news anchor throughout\r\n- Note what's technically suspicious or missing\r\n\r\n# Example flow:\r\n---\r\ntitle: \"Run a FREE ChatGPT type app on Your PC!\":\r\noutput: \"Use Ollama to install models like Llama locally, but expect slower performance and you'll need decent GPU power to make it actually useful!\" [THEN CONTINUE WITH FULL TECHNICAL STORY...]\r\n\r\ntitle: \"This One Weird AI Trick Is Changing Everything About How Computers Think.\":\r\noutput: \"RoPE (Rotational Position Embedding) was recently invented by Meta to use rotating vectors in mathematical space in addition to standard position numbers to improve LLMs' understanding of relationships between different words on long texts.\" [THEN CONTINUE WITH FULL TECHNICAL STORY...]\r\n\r\nThe first sentence should seamlessly continue whatever clickbait title precedes it.\r\n\r\nTranscript:\r\n---\r\n";
output += runtime.suppressValue(runtime.contextOrFrameLookup(context, frame, "transcript"), env.opts.autoescape);
output += "\r\n---\r\n\r\nWrite the completion sentence and full technical breakdown that would follow any relevant clickbait title.";
if(parentTemplate) {
parentTemplate.rootRenderFunc(env, context, frame, runtime, cb);
} else {
cb(null, output);
}
;
} catch (e) {
  cb(runtime.handleError(e, lineno, colno));
}
}
return {
root: root
};

})();
})();
(function() {(window.nunjucksPrecompiled = window.nunjucksPrecompiled || {})["title-response.njk"] = (function() {
function root(env, context, frame, runtime, cb) {
var lineno = 0;
var colno = 0;
var output = "";
try {
var parentTemplate = null;
output += "VIDEO TITLE: ";
output += runtime.suppressValue(runtime.contextOrFrameLookup(context, frame, "title"), env.opts.autoescape);
output += "\r\n\r\nTRANSCRIPT: ";
output += runtime.suppressValue(runtime.contextOrFrameLookup(context, frame, "transcript"), env.opts.autoescape);
output += "\r\n\r\nGenerate a Title Response - a single sentence that directly answers the question or promise made by this video title. Focus on the concrete outcome, result, or answer revealed in the video.\r\n\r\nTitle Response:";
if(parentTemplate) {
parentTemplate.rootRenderFunc(env, context, frame, runtime, cb);
} else {
cb(null, output);
}
;
} catch (e) {
  cb(runtime.handleError(e, lineno, colno));
}
}
return {
root: root
};

})();
})();
(function() {(window.nunjucksPrecompiled = window.nunjucksPrecompiled || {})["transcript-cleaner.njk"] = (function() {
function root(env, context, frame, runtime, cb) {
var lineno = 0;
var colno = 0;
var output = "";
try {
var parentTemplate = null;
output += "Please clean up this raw YouTube transcript. Remove filler words, fix sentence structure, and organize into readable paragraphs, but preserve every detail, fact, claim, and piece of information exactly as stated.\n\nRaw Transcript:\n---\n";
output += runtime.suppressValue(runtime.contextOrFrameLookup(context, frame, "transcript"), env.opts.autoescape);
output += "\n---\n\nReturn the cleaned transcript as flowing, readable text:";
if(parentTemplate) {
parentTemplate.rootRenderFunc(env, context, frame, runtime, cb);
} else {
cb(null, output);
}
;
} catch (e) {
  cb(runtime.handleError(e, lineno, colno));
}
}
return {
root: root
};

})();
})();
