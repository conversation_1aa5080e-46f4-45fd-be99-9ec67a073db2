!function(t,n){"object"==typeof exports&&"object"==typeof module?module.exports=n():"function"==typeof define&&define.amd?define([],n):"object"==typeof exports?exports.nunjucks=n():t.nunjucks=n()}("undefined"!=typeof self?self:this,function(){return function(t){var n={};function r(i){if(n[i])return n[i].exports;var e=n[i]={i:i,l:!1,exports:{}};return t[i].call(e.exports,e,e.exports,r),e.l=!0,e.exports}return r.m=t,r.c=n,r.d=function(t,n,i){r.o(t,n)||Object.defineProperty(t,n,{configurable:!1,enumerable:!0,get:i})},r.n=function(t){var n=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(n,"a",n),n},r.o=function(t,n){return Object.prototype.hasOwnProperty.call(t,n)},r.p="",r(r.s=6)}([function(t,n){},function(t,n,r){"use strict";var i=Array.prototype,e=Object.prototype,u={"&":"&amp;",'"':"&quot;","'":"&#39;","<":"&lt;",">":"&gt;"},o=/[&"'<>]/g;function f(t,n){return e.hasOwnProperty.call(t,n)}function c(t){return u[t]}function s(t,n,r){var i,e,u;if(t instanceof Error&&(t=(e=t).name+": "+e.message),Object.setPrototypeOf?Object.setPrototypeOf(i=Error(t),s.prototype):Object.defineProperty(i=this,"message",{enumerable:!1,writable:!0,value:t}),Object.defineProperty(i,"name",{value:"Template render error"}),Error.captureStackTrace&&Error.captureStackTrace(i,this.constructor),e){var o=Object.getOwnPropertyDescriptor(e,"stack");(u=o&&(o.get||function(){return o.value}))||(u=function(){return e.stack})}else{var f=Error(t).stack;u=function(){return f}}return Object.defineProperty(i,"stack",{get:function(){return u.call(i)}}),Object.defineProperty(i,"cause",{value:e}),i.lineno=n,i.colno=r,i.firstUpdate=!0,i.Update=function(t){var n="("+(t||"unknown path")+")";return this.firstUpdate&&(this.lineno&&this.colno?n+=" [Line "+this.lineno+", Column "+this.colno+"]":this.lineno&&(n+=" [Line "+this.lineno+"]")),n+="\n ",this.firstUpdate&&(n+=" "),this.message=n+(this.message||""),this.firstUpdate=!1,this},i}function a(t){return"[object Function]"===e.toString.call(t)}function h(t){return"[object Array]"===e.toString.call(t)}function l(t){return"[object String]"===e.toString.call(t)}function v(t){return"[object Object]"===e.toString.call(t)}function d(t){return Array.prototype.slice.call(t)}function p(t,n,r){return Array.prototype.indexOf.call(t||[],n,r)}function y(t){var n=[];for(var r in t)f(t,r)&&n.push(r);return n}(n=t.exports={}).hasOwnProp=f,n.t=function(t,r,i){if(i.Update||(i=new n.TemplateError(i)),i.Update(t),!r){var e=i;(i=Error(e.message)).name=e.name}return i},Object.setPrototypeOf?Object.setPrototypeOf(s.prototype,Error.prototype):s.prototype=Object.create(Error.prototype,{constructor:{value:s}}),n.TemplateError=s,n.escape=function(t){return t.replace(o,c)},n.isFunction=a,n.isArray=h,n.isString=l,n.isObject=v,n.groupBy=function(t,n,r){for(var i,e,u={},o=a(n)?n:(e=(i=n)?"string"==typeof i?i.split("."):[i]:[],function(t){for(var n=t,r=0;r<e.length;r++){var i=e[r];if(!f(n,i))return;n=n[i]}return n}),c=0;c<t.length;c++){var s=t[c],h=o(s,c);if(void 0===h&&!0===r)throw new TypeError('groupby: attribute "'+n+'" resolved to undefined');(u[h]||(u[h]=[])).push(s)}return u},n.toArray=d,n.without=function(t){var n=[];if(!t)return n;for(var r=t.length,i=d(arguments).slice(1),e=-1;++e<r;)-1===p(i,t[e])&&n.push(t[e]);return n},n.repeat=function(t,n){for(var r="",i=0;i<n;i++)r+=t;return r},n.each=function(t,n,r){if(null!=t)if(i.forEach&&t.forEach===i.forEach)t.forEach(n,r);else if(t.length===+t.length)for(var e=0,u=t.length;e<u;e++)n.call(r,t[e],e,t)},n.map=function(t,n){var r=[];if(null==t)return r;if(i.map&&t.map===i.map)return t.map(n);for(var e=0;e<t.length;e++)r[r.length]=n(t[e],e);return t.length===+t.length&&(r.length=t.length),r},n.asyncIter=function(t,n,r){var i=-1;!function e(){++i<t.length?n(t[i],i,e,r):r()}()},n.asyncFor=function(t,n,r){var i=y(t||{}),e=i.length,u=-1;!function o(){var f=i[++u];u<e?n(f,t[f],u,e,o):r()}()},n.indexOf=p,n.keys=y,n.r=function(t){return y(t).map(function(n){return[n,t[n]]})},n.u=function(t){return y(t).map(function(n){return t[n]})},n.f=n.extend=function(t,n){return t=t||{},y(n).forEach(function(r){t[r]=n[r]}),t},n.inOperator=function(t,n){if(h(n)||l(n))return-1!==n.indexOf(t);if(v(n))return t in n;throw Error('Cannot use "in" operator to search for "'+t+'" in unexpected types.')}},function(t,n,r){"use strict";var i=r(1),e=Array.from,u="function"==typeof Symbol&&Symbol.iterator&&"function"==typeof e,o=function(){function t(t,n){this.variables={},this.parent=t,this.topLevel=!1,this.isolateWrites=n}var n=t.prototype;return n.set=function(t,n,r){var i=t.split("."),e=this.variables,u=this;if(r&&(u=this.resolve(i[0],!0)))u.set(t,n);else{for(var o=0;o<i.length-1;o++){var f=i[o];e[f]||(e[f]={}),e=e[f]}e[i[i.length-1]]=n}},n.get=function(t){var n=this.variables[t];return void 0!==n?n:null},n.lookup=function(t){var n=this.parent,r=this.variables[t];return void 0!==r?r:n&&n.lookup(t)},n.resolve=function(t,n){var r=n&&this.isolateWrites?void 0:this.parent;return void 0!==this.variables[t]?this:r&&r.resolve(t)},n.push=function(n){return new t(this,n)},n.pop=function(){return this.parent},t}();function f(t){return t&&Object.prototype.hasOwnProperty.call(t,"__keywords")}function c(t){var n=t.length;return 0===n?0:f(t[n-1])?n-1:n}function s(t){if("string"!=typeof t)return t;this.val=t,this.length=t.length}s.prototype=Object.create(String.prototype,{length:{writable:!0,configurable:!0,value:0}}),s.prototype.valueOf=function(){return this.val},s.prototype.toString=function(){return this.val},t.exports={Frame:o,makeMacro:function(t,n,r){var i=this;return function(){for(var e=arguments.length,u=Array(e),o=0;o<e;o++)u[o]=arguments[o];var s,a=c(u),h=function(t){var n=t.length;if(n){var r=t[n-1];if(f(r))return r}return{}}(u);if(a>t.length)s=u.slice(0,t.length),u.slice(s.length,a).forEach(function(t,r){r<n.length&&(h[n[r]]=t)}),s.push(h);else if(a<t.length){s=u.slice(0,a);for(var l=a;l<t.length;l++){var v=t[l];s.push(h[v]),delete h[v]}s.push(h)}else s=u;return r.apply(i,s)}},makeKeywordArgs:function(t){return t.__keywords=!0,t},numArgs:c,suppressValue:function(t,n){return t=void 0!==t&&null!==t?t:"",!n||t instanceof s||(t=i.escape(t.toString())),t},ensureDefined:function(t,n,r){if(null===t||void 0===t)throw new i.TemplateError("attempted to output null or undefined value",n+1,r+1);return t},memberLookup:function(t,n){if(void 0!==t&&null!==t)return"function"==typeof t[n]?function(){for(var r=arguments.length,i=Array(r),e=0;e<r;e++)i[e]=arguments[e];return t[n].apply(t,i)}:t[n]},contextOrFrameLookup:function(t,n,r){var i=n.lookup(r);return void 0!==i?i:t.lookup(r)},callWrap:function(t,n,r,i){if(!t)throw Error("Unable to call `"+n+"`, which is undefined or falsey");if("function"!=typeof t)throw Error("Unable to call `"+n+"`, which is not a function");return t.apply(r,i)},handleError:function(t,n,r){return t.lineno?t:new i.TemplateError(t,n,r)},isArray:i.isArray,keys:i.keys,SafeString:s,copySafeness:function(t,n){return t instanceof s?new s(n):n.toString()},markSafe:function(t){var n=typeof t;return"string"===n?new s(t):"function"!==n?t:function(n){var r=t.apply(this,arguments);return"string"==typeof r?new s(r):r}},asyncEach:function(t,n,r,e){if(i.isArray(t)){var u=t.length;i.asyncIter(t,function(t,i,e){switch(n){case 1:r(t,i,u,e);break;case 2:r(t[0],t[1],i,u,e);break;case 3:r(t[0],t[1],t[2],i,u,e);break;default:t.push(i,u,e),r.apply(this,t)}},e)}else i.asyncFor(t,function(t,n,i,e,u){r(t,n,i,e,u)},e)},asyncAll:function(t,n,r,e){var u,o,f=0;function c(t,n){f++,o[t]=n,f===u&&e(null,o.join(""))}if(i.isArray(t))if(u=t.length,o=Array(u),0===u)e(null,"");else for(var s=0;s<t.length;s++){var a=t[s];switch(n){case 1:r(a,s,u,c);break;case 2:r(a[0],a[1],s,u,c);break;case 3:r(a[0],a[1],a[2],s,u,c);break;default:a.push(s,u,c),r.apply(this,a)}}else{var h=i.keys(t||{});if(u=h.length,o=Array(u),0===u)e(null,"");else for(var l=0;l<h.length;l++){var v=h[l];r(v,t[v],l,u,c)}}},inOperator:i.inOperator,fromIterator:function(t){return"object"!=typeof t||null===t||i.isArray(t)?t:u&&Symbol.iterator in t?e(t):t}}},function(t,n,r){"use strict";var i=function(t){var n,r;function i(n){var r;return(r=t.call(this)||this).precompiled=n||{},r}return r=t,(n=i).prototype=Object.create(r.prototype),n.prototype.constructor=n,n.__proto__=r,i.prototype.getSource=function(t){return this.precompiled[t]?{src:{type:"code",obj:this.precompiled[t]},path:t}:null},i}(r(4));t.exports={PrecompiledLoader:i}},function(t,n,r){"use strict";var i=r(0),e=r(5).EmitterObj;t.exports=function(t){var n,r;function e(){return t.apply(this,arguments)||this}r=t,(n=e).prototype=Object.create(r.prototype),n.prototype.constructor=n,n.__proto__=r;var u=e.prototype;return u.resolve=function(t,n){return i.resolve(i.dirname(t),n)},u.isRelative=function(t){return 0===t.indexOf("./")||0===t.indexOf("../")},e}(e)},function(t,n,r){"use strict";function i(t,n){for(var r=0;r<n.length;r++){var i=n[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function e(t,n,r){return n&&i(t.prototype,n),r&&i(t,r),t}function u(t,n){t.prototype=Object.create(n.prototype),t.prototype.constructor=t,t.__proto__=n}var o=r(13),f=r(1);function c(t,n,r){r=r||{},f.keys(r).forEach(function(n){var i,e;r[n]=(i=t.prototype[n],e=r[n],"function"!=typeof i||"function"!=typeof e?e:function(){var t=this.parent;this.parent=i;var n=e.apply(this,arguments);return this.parent=t,n})});var i=function(t){function r(){return t.apply(this,arguments)||this}return u(r,t),e(r,[{key:"typename",get:function(){return n}}]),r}(t);return f.f(i.prototype,r),i}var s=function(){function t(){this.init.apply(this,arguments)}return t.prototype.init=function(){},t.extend=function(t,n){return"object"==typeof t&&(n=t,t="anonymous"),c(this,t,n)},e(t,[{key:"typename",get:function(){return this.constructor.name}}]),t}(),a=function(t){function n(){var n,r;return(n=r=t.call(this)||this).init.apply(n,arguments),r}return u(n,t),n.prototype.init=function(){},n.extend=function(t,n){return"object"==typeof t&&(n=t,t="anonymous"),c(this,t,n)},e(n,[{key:"typename",get:function(){return this.constructor.name}}]),n}(o);t.exports={Obj:s,EmitterObj:a}},function(t,n,r){"use strict";var i,e=r(1),u=r(7),o=u.Environment,f=u.Template,c=r(4),s=r(3),a=r(0),h=r(0),l=r(0),v=r(0),d=r(2),p=r(0),y=r(17);function w(t,n){var r;return n=n||{},e.isObject(t)&&(n=t,t=null),s.FileSystemLoader?r=new s.FileSystemLoader(t,{watch:n.watch,noCache:n.noCache}):s.WebLoader&&(r=new s.WebLoader(t,{useCache:n.web&&n.web.useCache,async:n.web&&n.web.async})),i=new o(r,n),n&&n.express&&i.express(n.express),i}t.exports={Environment:o,Template:f,Loader:c,FileSystemLoader:s.FileSystemLoader,NodeResolveLoader:s.NodeResolveLoader,PrecompiledLoader:s.PrecompiledLoader,WebLoader:s.WebLoader,compiler:h,parser:l,lexer:v,runtime:d,lib:e,nodes:p,installJinjaCompat:y,configure:w,reset:function(){i=void 0},compile:function(t,n,r,e){return i||w(),new f(t,n,r,e)},render:function(t,n,r){return i||w(),i.render(t,n,r)},renderString:function(t,n,r){return i||w(),i.renderString(t,n,r)},precompile:a?a.precompile:void 0,precompileString:a?a.precompileString:void 0}},function(t,n,r){"use strict";function i(t,n){t.prototype=Object.create(n.prototype),t.prototype.constructor=t,t.__proto__=n}var e=r(8),u=r(11),o=r(1),f=r(0),c=r(12),s=r(3),a=s.FileSystemLoader,h=s.WebLoader,l=s.PrecompiledLoader,v=r(14),d=r(15),p=r(5),y=p.Obj,w=p.EmitterObj,b=r(2),m=b.handleError,g=b.Frame,E=r(16);function j(t,n,r){e(function(){t(n,r)})}var k={type:"code",obj:{root:function(t,n,r,i,e){try{e(null,"")}catch(t){e(m(t,null,null))}}}},O=function(t){function n(){return t.apply(this,arguments)||this}i(n,t);var r=n.prototype;return r.init=function(t,n){var r=this;n=this.opts=n||{},this.opts.dev=!!n.dev,this.opts.autoescape=null==n.autoescape||n.autoescape,this.opts.throwOnUndefined=!!n.throwOnUndefined,this.opts.trimBlocks=!!n.trimBlocks,this.opts.lstripBlocks=!!n.lstripBlocks,this.loaders=[],t?this.loaders=o.isArray(t)?t:[t]:a?this.loaders=[new a("views")]:h&&(this.loaders=[new h("/views")]),"undefined"!=typeof window&&window.nunjucksPrecompiled&&this.loaders.unshift(new l(window.nunjucksPrecompiled)),this.a(),this.globals=d(),this.filters={},this.tests={},this.asyncFilters=[],this.extensions={},this.extensionsList=[],o.r(c).forEach(function(t){var n=t[0],i=t[1];return r.addFilter(n,i)}),o.r(v).forEach(function(t){var n=t[0],i=t[1];return r.addTest(n,i)})},r.a=function(){var t=this;this.loaders.forEach(function(n){n.cache={},"function"==typeof n.on&&(n.on("update",function(r,i){n.cache[r]=null,t.emit("update",r,i,n)}),n.on("load",function(r,i){t.emit("load",r,i,n)}))})},r.invalidateCache=function(){this.loaders.forEach(function(t){t.cache={}})},r.addExtension=function(t,n){return n.__name=t,this.extensions[t]=n,this.extensionsList.push(n),this},r.removeExtension=function(t){var n=this.getExtension(t);n&&(this.extensionsList=o.without(this.extensionsList,n),delete this.extensions[t])},r.getExtension=function(t){return this.extensions[t]},r.hasExtension=function(t){return!!this.extensions[t]},r.addGlobal=function(t,n){return this.globals[t]=n,this},r.getGlobal=function(t){if(void 0===this.globals[t])throw Error("global not found: "+t);return this.globals[t]},r.addFilter=function(t,n,r){var i=n;return r&&this.asyncFilters.push(t),this.filters[t]=i,this},r.getFilter=function(t){if(!this.filters[t])throw Error("filter not found: "+t);return this.filters[t]},r.addTest=function(t,n){return this.tests[t]=n,this},r.getTest=function(t){if(!this.tests[t])throw Error("test not found: "+t);return this.tests[t]},r.resolveTemplate=function(t,n,r){return!(!t.isRelative||!n)&&t.isRelative(r)&&t.resolve?t.resolve(n,r):r},r.getTemplate=function(t,n,r,i,e){var u,f=this,c=this,s=null;if(t&&t.raw&&(t=t.raw),o.isFunction(r)&&(e=r,r=null,n=n||!1),o.isFunction(n)&&(e=n,n=!1),t instanceof S)s=t;else{if("string"!=typeof t)throw Error("template names must be a string: "+t);for(var a=0;a<this.loaders.length;a++){var h=this.loaders[a];if(s=h.cache[this.resolveTemplate(h,r,t)])break}}if(s)return n&&s.compile(),e?void e(null,s):s;return o.asyncIter(this.loaders,function(n,i,e,u){function o(t,r){t?u(t):r?(r.loader=n,u(null,r)):e()}t=c.resolveTemplate(n,r,t),n.async?n.getSource(t,o):o(null,n.getSource(t))},function(r,o){if(o||r||i||(r=Error("template not found: "+t)),r){if(e)return void e(r);throw r}var c;o?(c=new S(o.src,f,o.path,n),o.noCache||(o.loader.cache[t]=c)):c=new S(k,f,"",n),e?e(null,c):u=c}),u},r.express=function(t){return E(this,t)},r.render=function(t,n,r){o.isFunction(n)&&(r=n,n=null);var i=null;return this.getTemplate(t,function(t,e){if(t&&r)j(r,t);else{if(t)throw t;i=e.render(n,r)}}),i},r.renderString=function(t,n,r,i){return o.isFunction(r)&&(i=r,r={}),new S(t,this,(r=r||{}).path).render(n,i)},r.waterfall=function(t,n,r){return u(t,n,r)},n}(w),A=function(t){function n(){return t.apply(this,arguments)||this}i(n,t);var r=n.prototype;return r.init=function(t,n,r){var i=this;this.env=r||new O,this.ctx=o.extend({},t),this.blocks={},this.exported=[],o.keys(n).forEach(function(t){i.addBlock(t,n[t])})},r.lookup=function(t){return t in this.env.globals&&!(t in this.ctx)?this.env.globals[t]:this.ctx[t]},r.setVariable=function(t,n){this.ctx[t]=n},r.getVariables=function(){return this.ctx},r.addBlock=function(t,n){return this.blocks[t]=this.blocks[t]||[],this.blocks[t].push(n),this},r.getBlock=function(t){if(!this.blocks[t])throw Error('unknown block "'+t+'"');return this.blocks[t][0]},r.getSuper=function(t,n,r,i,e,u){var f=o.indexOf(this.blocks[n]||[],r),c=this.blocks[n][f+1];if(-1===f||!c)throw Error('no super block available for "'+n+'"');c(t,this,i,e,u)},r.addExport=function(t){this.exported.push(t)},r.getExported=function(){var t=this,n={};return this.exported.forEach(function(r){n[r]=t.ctx[r]}),n},n}(y),S=function(t){function n(){return t.apply(this,arguments)||this}i(n,t);var r=n.prototype;return r.init=function(t,n,r,i){if(this.env=n||new O,o.isObject(t))switch(t.type){case"code":this.tmplProps=t.obj;break;case"string":this.tmplStr=t.obj;break;default:throw Error("Unexpected template object type "+t.type+"; expected 'code', or 'string'")}else{if(!o.isString(t))throw Error("src must be a string or an object describing the source");this.tmplStr=t}if(this.path=r,i)try{this.h()}catch(t){throw o.t(this.path,this.env.opts.dev,t)}else this.compiled=!1},r.render=function(t,n,r){var i=this;"function"==typeof t?(r=t,t={}):"function"==typeof n&&(r=n,n=null);var e=!n;try{this.compile()}catch(t){var u=o.t(this.path,this.env.opts.dev,t);if(r)return j(r,u);throw u}var f=new A(t||{},this.blocks,this.env),c=n?n.push(!0):new g;c.topLevel=!0;var s=null,a=!1;return this.rootRenderFunc(this.env,f,c,b,function(t,n){if(!a||!r||void 0===n)if(t&&(t=o.t(i.path,i.env.opts.dev,t),a=!0),r)e?j(r,t,n):r(t,n);else{if(t)throw t;s=n}}),s},r.getExported=function(t,n,r){"function"==typeof t&&(r=t,t={}),"function"==typeof n&&(r=n,n=null);try{this.compile()}catch(t){if(r)return r(t);throw t}var i=n?n.push():new g;i.topLevel=!0;var e=new A(t||{},this.blocks,this.env);this.rootRenderFunc(this.env,e,i,b,function(t){t?r(t,null):r(null,e.getExported())})},r.compile=function(){this.compiled||this.h()},r.h=function(){var t;if(this.tmplProps)t=this.tmplProps;else{var n=f.compile(this.tmplStr,this.env.asyncFilters,this.env.extensionsList,this.path,this.env.opts);t=Function(n)()}this.blocks=this.v(t),this.rootRenderFunc=t.root,this.compiled=!0},r.v=function(t){var n={};return o.keys(t).forEach(function(r){"b_"===r.slice(0,2)&&(n[r.slice(2)]=t[r])}),n},n}(y);t.exports={Environment:O,Template:S}},function(t,n,r){"use strict";var i=r(9),e=[],u=[],o=i.makeRequestCallFromTimer(function(){if(u.length)throw u.shift()});function f(t){var n;(n=e.length?e.pop():new c).task=t,i(n)}function c(){this.task=null}t.exports=f,c.prototype.call=function(){try{this.task.call()}catch(t){f.onerror?f.onerror(t):(u.push(t),o())}finally{this.task=null,e[e.length]=this}}},function(t,n,r){"use strict";!function(n){function r(t){e.length||(i(),!0),e[e.length]=t}t.exports=r;var i,e=[],u=0,o=1024;function f(){for(;u<e.length;){var t=u;if(u+=1,e[t].call(),u>o){for(var n=0,r=e.length-u;n<r;n++)e[n]=e[n+u];e.length-=u,u=0}}e.length=0,u=0,!1}var c,s,a,h=void 0!==n?n:self,l=h.MutationObserver||h.WebKitMutationObserver;function v(t){return function(){var n=setTimeout(i,0),r=setInterval(i,50);function i(){clearTimeout(n),clearInterval(r),t()}}}"function"==typeof l?(c=1,s=new l(f),a=document.createTextNode(""),s.observe(a,{characterData:!0}),i=function(){c=-c,a.data=c}):i=v(f),r.requestFlush=i,r.makeRequestCallFromTimer=v}(r(10))},function(t,n){var r;r=function(){return this}();try{r=r||Function("return this")()||(0,eval)("this")}catch(t){"object"==typeof window&&(r=window)}t.exports=r},function(t,n,r){var i;!function(r){"use strict";var e=function(){var t=Array.prototype.slice.call(arguments);"function"==typeof t[0]&&t[0].apply(null,t.splice(1))},u=function(t){"function"==typeof setImmediate?setImmediate(t):"undefined"!=typeof process&&process.nextTick?process.nextTick(t):setTimeout(t,0)},o=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)},f=function(t,n,r){var i=r?u:e;if(n=n||function(){},!o(t))return n(Error("First argument to waterfall must be an array of functions"));if(!t.length)return n();var f=function(t){return function(r){if(r)n.apply(null,arguments),n=function(){};else{var e=Array.prototype.slice.call(arguments,1),u=t.next();u?e.push(f(u)):e.push(n),i(function(){t.apply(null,e)})}}};f(function(t){var n=function(r){var i=function(){return t.length&&t[r].apply(null,arguments),i.next()};return i.next=function(){return r<t.length-1?n(r+1):null},i};return n(0)}(t))()};void 0===(i=function(){return f}.apply(n,[]))||(t.exports=i)}()},function(t,n,r){"use strict";var i=r(1),e=r(2);function u(t,n){return null===t||void 0===t||!1===t?n:t}function o(t){return t!=t}function f(t){var n=(t=u(t,"")).toLowerCase();return e.copySafeness(t,n.charAt(0).toUpperCase()+n.slice(1))}function c(t){if(i.isString(t))return t.split("");if(i.isObject(t))return i.r(t||{}).map(function(t){return{key:t[0],value:t[1]}});if(i.isArray(t))return t;throw new i.TemplateError("list filter: type not iterable")}function s(t){return function(n,r,e){void 0===r&&(r="truthy");var u=this,o=u.env.getTest(r);return i.toArray(n).filter(function(n){return o.call(u,n,e)===t})}}function a(t){return e.copySafeness(t,t.replace(/^\s*|\s*$/g,""))}(n=t.exports={}).abs=Math.abs,n.batch=function(t,n,r){var i,e=[],u=[];for(i=0;i<t.length;i++)i%n==0&&u.length&&(e.push(u),u=[]),u.push(t[i]);if(u.length){if(r)for(i=u.length;i<n;i++)u.push(r);e.push(u)}return e},n.capitalize=f,n.center=function(t,n){if(t=u(t,""),n=n||80,t.length>=n)return t;var r=n-t.length,o=i.repeat(" ",r/2-r%2),f=i.repeat(" ",r/2);return e.copySafeness(t,o+t+f)},n.default=function(t,n,r){return r?t||n:void 0!==t?t:n},n.dictsort=function(t,n,r){if(!i.isObject(t))throw new i.TemplateError("dictsort filter: val must be an object");var e,u=[];for(var o in t)u.push([o,t[o]]);if(void 0===r||"key"===r)e=0;else{if("value"!==r)throw new i.TemplateError("dictsort filter: You can only sort by either key or value");e=1}return u.sort(function(t,r){var u=t[e],o=r[e];return n||(i.isString(u)&&(u=u.toUpperCase()),i.isString(o)&&(o=o.toUpperCase())),u>o?1:u===o?0:-1}),u},n.dump=function(t,n){return JSON.stringify(t,null,n)},n.escape=function(t){return t instanceof e.SafeString?t:(t=null===t||void 0===t?"":t,e.markSafe(i.escape(t.toString())))},n.safe=function(t){return t instanceof e.SafeString?t:(t=null===t||void 0===t?"":t,e.markSafe(t.toString()))},n.first=function(t){return t[0]},n.forceescape=function(t){return t=null===t||void 0===t?"":t,e.markSafe(i.escape(t.toString()))},n.groupby=function(t,n){return i.groupBy(t,n,this.env.opts.throwOnUndefined)},n.indent=function(t,n,r){if(""===(t=u(t,"")))return"";n=n||4;var o=t.split("\n"),f=i.repeat(" ",n),c=o.map(function(t,n){return 0!==n||r?""+f+t:t}).join("\n");return e.copySafeness(t,c)},n.join=function(t,n,r){return n=n||"",r&&(t=i.map(t,function(t){return t[r]})),t.join(n)},n.last=function(t){return t[t.length-1]},n.length=function(t){var n=u(t,"");return void 0!==n?"function"==typeof Map&&n instanceof Map||"function"==typeof Set&&n instanceof Set?n.size:!i.isObject(n)||n instanceof e.SafeString?n.length:i.keys(n).length:0},n.list=c,n.lower=function(t){return(t=u(t,"")).toLowerCase()},n.nl2br=function(t){return null===t||void 0===t?"":e.copySafeness(t,t.replace(/\r\n|\n/g,"<br />\n"))},n.random=function(t){return t[Math.floor(Math.random()*t.length)]},n.reject=s(!1),n.rejectattr=function(t,n){return t.filter(function(t){return!t[n]})},n.select=s(!0),n.selectattr=function(t,n){return t.filter(function(t){return!!t[n]})},n.replace=function(t,n,r,i){var u=t;if(n instanceof RegExp)return t.replace(n,r);void 0===i&&(i=-1);var o="";if("number"==typeof n)n=""+n;else if("string"!=typeof n)return t;if("number"==typeof t&&(t=""+t),"string"!=typeof t&&!(t instanceof e.SafeString))return t;if(""===n)return o=r+t.split("").join(r)+r,e.copySafeness(t,o);var f=t.indexOf(n);if(0===i||-1===f)return t;for(var c=0,s=0;f>-1&&(-1===i||s<i);)o+=t.substring(c,f)+r,c=f+n.length,s++,f=t.indexOf(n,c);return c<t.length&&(o+=t.substring(c)),e.copySafeness(u,o)},n.reverse=function(t){var n;return(n=i.isString(t)?c(t):i.map(t,function(t){return t})).reverse(),i.isString(t)?e.copySafeness(t,n.join("")):n},n.round=function(t,n,r){var i=Math.pow(10,n=n||0);return("ceil"===r?Math.ceil:"floor"===r?Math.floor:Math.round)(t*i)/i},n.slice=function(t,n,r){for(var i=Math.floor(t.length/n),e=t.length%n,u=[],o=0,f=0;f<n;f++){var c=o+f*i;f<e&&o++;var s=o+(f+1)*i,a=t.slice(c,s);r&&f>=e&&a.push(r),u.push(a)}return u},n.sum=function(t,n,r){return void 0===r&&(r=0),n&&(t=i.map(t,function(t){return t[n]})),r+t.reduce(function(t,n){return t+n},0)},n.sort=e.makeMacro(["value","reverse","case_sensitive","attribute"],[],function(t,n,r,e){var u=i.map(t,function(t){return t});return u.sort(function(t,u){var o=e?t[e]:t,f=e?u[e]:u;return!r&&i.isString(o)&&i.isString(f)&&(o=o.toLowerCase(),f=f.toLowerCase()),o<f?n?1:-1:o>f?n?-1:1:0}),u}),n.string=function(t){return e.copySafeness(t,t)},n.striptags=function(t,n){var r=a((t=u(t,"")).replace(/<\/?([a-z][a-z0-9]*)\b[^>]*>|<!--[\s\S]*?-->/gi,"")),i="";return i=n?r.replace(/^ +| +$/gm,"").replace(/ +/g," ").replace(/(\r\n)/g,"\n").replace(/\n\n\n+/g,"\n\n"):r.replace(/\s+/gi," "),e.copySafeness(t,i)},n.title=function(t){var n=(t=u(t,"")).split(" ").map(function(t){return f(t)});return e.copySafeness(t,n.join(" "))},n.trim=a,n.truncate=function(t,n,r,i){var o=t;if(t=u(t,""),n=n||255,t.length<=n)return t;if(r)t=t.substring(0,n);else{var f=t.lastIndexOf(" ",n);-1===f&&(f=n),t=t.substring(0,f)}return t+=void 0!==i&&null!==i?i:"...",e.copySafeness(o,t)},n.upper=function(t){return(t=u(t,"")).toUpperCase()},n.urlencode=function(t){var n=encodeURIComponent;return i.isString(t)?n(t):(i.isArray(t)?t:i.r(t)).map(function(t){var r=t[0],i=t[1];return n(r)+"="+n(i)}).join("&")};var h=/^(?:\(|<|&lt;)?(.*?)(?:\.|,|\)|\n|&gt;)?$/,l=/^[\w.!#$%&'*+\-\/=?\^`{|}~]+@[a-z\d\-]+(\.[a-z\d\-]+)+$/i,v=/^https?:\/\/.*$/,d=/^www\./,p=/\.(?:org|net|com)(?:\:|\/|$)/;n.urlize=function(t,n,r){o(n)&&(n=1/0);var i=!0===r?' rel="nofollow"':"";return t.split(/(\s+)/).filter(function(t){return t&&t.length}).map(function(t){var r=t.match(h),e=r?r[1]:t,u=e.substr(0,n);return v.test(e)?'<a href="'+e+'"'+i+">"+u+"</a>":d.test(e)?'<a href="http://'+e+'"'+i+">"+u+"</a>":l.test(e)?'<a href="mailto:'+e+'">'+e+"</a>":p.test(e)?'<a href="http://'+e+'"'+i+">"+u+"</a>":t}).join("")},n.wordcount=function(t){var n=(t=u(t,""))?t.match(/\w+/g):null;return n?n.length:null},n.float=function(t,n){var r=parseFloat(t);return o(r)?n:r},n.int=function(t,n){var r=parseInt(t,10);return o(r)?n:r},n.d=n.default,n.e=n.escape},function(t,n){function r(){this.y=this.y||{},this.w=this.w||void 0}function i(t){return"function"==typeof t}function e(t){return"object"==typeof t&&null!==t}function u(t){return void 0===t}t.exports=r,r.EventEmitter=r,r.prototype.y=void 0,r.prototype.w=void 0,r.defaultMaxListeners=10,r.prototype.setMaxListeners=function(t){if("number"!=typeof t||t<0||isNaN(t))throw TypeError("n must be a positive number");return this.w=t,this},r.prototype.emit=function(t){var n,r,o,f,c,s;if(this.y||(this.y={}),"error"===t&&(!this.y.error||e(this.y.error)&&!this.y.error.length)){if((n=arguments[1])instanceof Error)throw n;var a=Error('Uncaught, unspecified "error" event. ('+n+")");throw a.context=n,a}if(u(r=this.y[t]))return!1;if(i(r))switch(arguments.length){case 1:r.call(this);break;case 2:r.call(this,arguments[1]);break;case 3:r.call(this,arguments[1],arguments[2]);break;default:f=Array.prototype.slice.call(arguments,1),r.apply(this,f)}else if(e(r))for(f=Array.prototype.slice.call(arguments,1),o=(s=r.slice()).length,c=0;c<o;c++)s[c].apply(this,f);return!0},r.prototype.addListener=function(t,n){var o;if(!i(n))throw TypeError("listener must be a function");return this.y||(this.y={}),this.y.newListener&&this.emit("newListener",t,i(n.listener)?n.listener:n),this.y[t]?e(this.y[t])?this.y[t].push(n):this.y[t]=[this.y[t],n]:this.y[t]=n,e(this.y[t])&&!this.y[t].warned&&(o=u(this.w)?r.defaultMaxListeners:this.w)&&o>0&&this.y[t].length>o&&(this.y[t].warned=!0,console.error("(node) warning: possible EventEmitter memory leak detected. %d listeners added. Use emitter.setMaxListeners() to increase limit.",this.y[t].length),"function"==typeof console.trace&&console.trace()),this},r.prototype.on=r.prototype.addListener,r.prototype.once=function(t,n){if(!i(n))throw TypeError("listener must be a function");var r=!1;function e(){this.removeListener(t,e),r||(r=!0,n.apply(this,arguments))}return e.listener=n,this.on(t,e),this},r.prototype.removeListener=function(t,n){var r,u,o,f;if(!i(n))throw TypeError("listener must be a function");if(!this.y||!this.y[t])return this;if(o=(r=this.y[t]).length,u=-1,r===n||i(r.listener)&&r.listener===n)delete this.y[t],this.y.removeListener&&this.emit("removeListener",t,n);else if(e(r)){for(f=o;f-- >0;)if(r[f]===n||r[f].listener&&r[f].listener===n){u=f;break}if(u<0)return this;1===r.length?(r.length=0,delete this.y[t]):r.splice(u,1),this.y.removeListener&&this.emit("removeListener",t,n)}return this},r.prototype.removeAllListeners=function(t){var n,r;if(!this.y)return this;if(!this.y.removeListener)return 0===arguments.length?this.y={}:this.y[t]&&delete this.y[t],this;if(0===arguments.length){for(n in this.y)"removeListener"!==n&&this.removeAllListeners(n);return this.removeAllListeners("removeListener"),this.y={},this}if(i(r=this.y[t]))this.removeListener(t,r);else if(r)for(;r.length;)this.removeListener(t,r[r.length-1]);return delete this.y[t],this},r.prototype.listeners=function(t){return this.y&&this.y[t]?i(this.y[t])?[this.y[t]]:this.y[t].slice():[]},r.prototype.listenerCount=function(t){if(this.y){var n=this.y[t];if(i(n))return 1;if(n)return n.length}return 0},r.listenerCount=function(t,n){return t.listenerCount(n)}},function(t,n,r){"use strict";var i=r(2).SafeString;n.callable=function(t){return"function"==typeof t},n.defined=function(t){return void 0!==t},n.divisibleby=function(t,n){return t%n==0},n.escaped=function(t){return t instanceof i},n.equalto=function(t,n){return t===n},n.eq=n.equalto,n.sameas=n.equalto,n.even=function(t){return t%2==0},n.falsy=function(t){return!t},n.ge=function(t,n){return t>=n},n.greaterthan=function(t,n){return t>n},n.gt=n.greaterthan,n.le=function(t,n){return t<=n},n.lessthan=function(t,n){return t<n},n.lt=n.lessthan,n.lower=function(t){return t.toLowerCase()===t},n.ne=function(t,n){return t!==n},n.null=function(t){return null===t},n.number=function(t){return"number"==typeof t},n.odd=function(t){return t%2==1},n.string=function(t){return"string"==typeof t},n.truthy=function(t){return!!t},n.undefined=function(t){return void 0===t},n.upper=function(t){return t.toUpperCase()===t},n.iterable=function(t){return"undefined"!=typeof Symbol?!!t[Symbol.iterator]:Array.isArray(t)||"string"==typeof t},n.mapping=function(t){var n=null!==t&&void 0!==t&&"object"==typeof t&&!Array.isArray(t);return Set?n&&!(t instanceof Set):n}},function(t,n,r){"use strict";t.exports=function(){return{range:function(t,n,r){void 0===n?(n=t,t=0,r=1):r||(r=1);var i=[];if(r>0)for(var e=t;e<n;e+=r)i.push(e);else for(var u=t;u>n;u+=r)i.push(u);return i},cycler:function(){return t=Array.prototype.slice.call(arguments),n=-1,{current:null,reset:function(){n=-1,this.current=null},next:function(){return++n>=t.length&&(n=0),this.current=t[n],this.current}};var t,n},joiner:function(t){return function(t){t=t||",";var n=!0;return function(){var r=n?"":t;return n=!1,r}}(t)}}}},function(t,n,r){var i=r(0);t.exports=function(t,n){function r(t,n){if(this.name=t,this.path=t,this.defaultEngine=n.defaultEngine,this.ext=i.extname(t),!this.ext&&!this.defaultEngine)throw Error("No default engine was specified and no extension was provided.");this.ext||(this.name+=this.ext=("."!==this.defaultEngine[0]?".":"")+this.defaultEngine)}return r.prototype.render=function(n,r){t.render(this.name,n,r)},n.set("view",r),n.set("nunjucksEnv",t),t}},function(t,n,r){t.exports=function(){"use strict";var t,n,r=this.runtime,i=this.lib,e=this.compiler.Compiler,u=this.parser.Parser,o=(this.nodes,this.lexer,r.contextOrFrameLookup),f=r.memberLookup;function c(t,n){return Object.prototype.hasOwnProperty.call(t,n)}e&&(t=e.prototype.assertType),u&&(n=u.prototype.parseAggregate),r.contextOrFrameLookup=function(t,n,r){var i=o.apply(this,arguments);if(void 0!==i)return i;switch(r){case"True":return!0;case"False":return!1;case"None":return null;default:return}};var s={pop:function(t){if(void 0===t)return this.pop();if(t>=this.length||t<0)throw Error("KeyError");return this.splice(t,1)},append:function(t){return this.push(t)},remove:function(t){for(var n=0;n<this.length;n++)if(this[n]===t)return this.splice(n,1);throw Error("ValueError")},count:function(t){for(var n=0,r=0;r<this.length;r++)this[r]===t&&n++;return n},index:function(t){var n;if(-1===(n=this.indexOf(t)))throw Error("ValueError");return n},find:function(t){return this.indexOf(t)},insert:function(t,n){return this.splice(t,0,n)}},a={items:function(){return i.r(this)},values:function(){return i.u(this)},keys:function(){return i.keys(this)},get:function(t,n){var r=this[t];return void 0===r&&(r=n),r},has_key:function(t){return c(this,t)},pop:function(t,n){var r=this[t];if(void 0===r&&void 0!==n)r=n;else{if(void 0===r)throw Error("KeyError");delete this[t]}return r},popitem:function(){var t=i.keys(this);if(!t.length)throw Error("KeyError");var n=t[0],r=this[n];return delete this[n],[n,r]},setdefault:function(t,n){return void 0===n&&(n=null),t in this||(this[t]=n),this[t]},update:function(t){return i.f(this,t),null}};return a.iteritems=a.items,a.itervalues=a.values,a.iterkeys=a.keys,r.memberLookup=function(t,n,e){return 4===arguments.length?function(t,n,i,e){t=t||[],null===n&&(n=e<0?t.length-1:0),null===i?i=e<0?-1:t.length:i<0&&(i+=t.length),n<0&&(n+=t.length);for(var u=[],o=n;!(o<0||o>t.length||e>0&&o>=i||e<0&&o<=i);o+=e)u.push(r.memberLookup(t,o));return u}.apply(this,arguments):(t=t||{},i.isArray(t)&&c(s,n)?s[n].bind(t):i.isObject(t)&&c(a,n)?a[n].bind(t):f.apply(this,arguments))},function(){r.contextOrFrameLookup=o,r.memberLookup=f,e&&(e.prototype.assertType=t),u&&(u.prototype.parseAggregate=n)}}}])});
//# sourceMappingURL=nunjucks-slim.min.js.map