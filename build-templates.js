const nunjucks = require('nunjucks');
const fs = require('fs');
const path = require('path');

// Configure Nunjucks
const env = nunjucks.configure('templates', {
  autoescape: false,
  throwOnUndefined: false
});

// Auto-discover all .njk files in templates directory
const templatesDir = path.join(__dirname, 'templates');
const templateFiles = fs.readdirSync(templatesDir)
  .filter(file => file.endsWith('.njk'))
  .map(file => file);

console.log(`Found ${templateFiles.length} template files:`, templateFiles);

// Create lib directory if it doesn't exist
const libDir = path.join(__dirname, 'lib');
if (!fs.existsSync(libDir)) {
  fs.mkdirSync(libDir, { recursive: true });
}

// Process each template file to extract system prompts and user templates
const systemPrompts = {};
const userTemplates = [];

templateFiles.forEach(templateFile => {
  const templatePath = path.join(templatesDir, templateFile);
  const content = fs.readFileSync(templatePath, 'utf8');
  
  // Split on # user: marker
  const parts = content.split(/^# user:\s*$/m);
  
  if (parts.length !== 2) {
    console.warn(`Warning: ${templateFile} does not have proper # system: / # user: format`);
    return;
  }
  
  // Extract system prompt (remove # system: marker)
  const systemPrompt = parts[0].replace(/^# system:\s*/m, '').trim();
  
  // Extract user template
  const userTemplate = parts[1].trim();
  
  // Store system prompt with template name as key (without .njk extension)
  const templateName = templateFile.replace('.njk', '');
  systemPrompts[templateName] = systemPrompt;
  
  // Create temporary user template file for compilation
  const tempUserPath = path.join(templatesDir, `temp_${templateFile}`);
  fs.writeFileSync(tempUserPath, userTemplate);
  userTemplates.push(`temp_${templateFile}`);
  
  console.log(`Processed ${templateFile}: system prompt (${systemPrompt.length} chars), user template (${userTemplate.length} chars)`);
});

// Precompile user templates
const precompiledTemplates = nunjucks.precompile('templates', {
  include: userTemplates
});

// Clean up temporary files and fix template names in compiled output
userTemplates.forEach(tempFile => {
  const tempPath = path.join(templatesDir, tempFile);
  fs.unlinkSync(tempPath);
});

// Fix template names in compiled output (remove temp_ prefix)
const fixedPrecompiledTemplates = precompiledTemplates.replace(/temp_/g, '');

// Write precompiled user templates
fs.writeFileSync(
  path.join(libDir, 'templates-precompiled.js'),
  fixedPrecompiledTemplates
);

// Generate system prompts export file
const systemPromptsExport = `// Auto-generated system prompts from .njk templates
// Do not edit this file directly - edit the .njk files and run build-templates.js

${Object.entries(systemPrompts).map(([name, prompt]) => {
  const constantName = name.toUpperCase().replace(/-/g, '_') + '_SYSTEM_PROMPT';
  return `const ${constantName} = \`${prompt.replace(/`/g, '\\`')}\`;`;
}).join('\n\n')}

// Export for both Node.js and browser environments
if (typeof module !== "undefined" && module.exports) {
  // Node.js environment
  module.exports = {
${Object.keys(systemPrompts).map(name => {
  const constantName = name.toUpperCase().replace(/-/g, '_') + '_SYSTEM_PROMPT';
  return `    ${constantName}`;
}).join(',\n')}
  };
} else if (typeof window !== "undefined") {
  // Browser environment
${Object.entries(systemPrompts).map(([name, prompt]) => {
  const constantName = name.toUpperCase().replace(/-/g, '_') + '_SYSTEM_PROMPT';
  const globalName = name.toUpperCase().replace(/-/g, '_') + '_PROMPTS';
  return `  window.${globalName} = { SYSTEM_PROMPT: ${constantName} };`;
}).join('\n')}
}
`;

fs.writeFileSync(
  path.join(libDir, 'system-prompts.js'),
  systemPromptsExport
);

console.log(`Templates processed successfully!`);
console.log(`- Generated lib/templates-precompiled.js with ${templateFiles.length} compiled user templates`);
console.log(`- Generated lib/system-prompts.js with ${Object.keys(systemPrompts).length} system prompts`);
console.log(`- System prompts: ${Object.keys(systemPrompts).join(', ')}`);