// Browser-compatible investigative reporter service

// Import prompts from shared templates
// Note: prompts/investigative-reporter-prompts.js must be loaded before this script in manifest.json

async function transformTranscriptToArticle(cleanTranscript) {
  if (
    !cleanTranscript ||
    typeof cleanTranscript !== "string" ||
    cleanTranscript.trim() === ""
  ) {
    throw new Error("Clean transcript must be a non-empty string");
  }

  // DEBUG: Log the clean transcript being processed
  console.log("[YT-Bookmarker] DEBUG: Clean transcript being sent to AI for article generation:");
  console.log("Length:", cleanTranscript.length);
  console.log("First 500 chars:", cleanTranscript.substring(0, 500));
  console.log("Last 200 chars:", cleanTranscript.substring(cleanTranscript.length - 200));

  // Get API key from storage
  const result = await browser.storage.local.get("openai_api_key");
  const apiKey = result.openai_api_key;

  if (!apiKey) {
    throw new Error("OpenAI API key not configured");
  }

  const openai = new BrowserOpenAIService({ apiKey });

  // Get system prompt from shared templates
  if (!window.INVESTIGATIVE_REPORTER_PROMPTS) {
    console.error("[YT-Bookmarker] INVESTIGATIVE_REPORTER_PROMPTS not available. Available globals:", Object.keys(window));
    throw new Error("INVESTIGATIVE_REPORTER_PROMPTS not loaded - investigative-reporter-prompts.js may not have loaded properly");
  }
  
  if (!window.INVESTIGATIVE_REPORTER_PROMPTS.SYSTEM_PROMPT) {
    console.error("[YT-Bookmarker] SYSTEM_PROMPT missing from INVESTIGATIVE_REPORTER_PROMPTS:", window.INVESTIGATIVE_REPORTER_PROMPTS);
    throw new Error("SYSTEM_PROMPT missing from INVESTIGATIVE_REPORTER_PROMPTS");
  }
  
  const { SYSTEM_PROMPT } = window.INVESTIGATIVE_REPORTER_PROMPTS;

  // Use the CSP-safe template service with precompiled templates
  if (typeof NunjucksService === 'undefined') {
    throw new Error("Template service not available - nunjucks-service.js may not have loaded");
  }

  const templateService = new NunjucksService();
  const userPrompt = templateService.renderTemplate('investigative-reporter.njk', { transcript: cleanTranscript });

  // DEBUG: Log the full prompt being sent
  console.log("[YT-Bookmarker] DEBUG: Full prompt being sent to OpenAI:");
  console.log("System prompt length:", SYSTEM_PROMPT.length);
  console.log("User prompt length:", userPrompt.length);
  console.log("User prompt preview:", userPrompt.substring(0, 1000) + "...");

  const response = await openai.withSystem(SYSTEM_PROMPT, userPrompt, {
    model: "gpt-4o-mini",
    temperature: 0.7,
    max_tokens: 2000,
  });

  const articleContent = response.getContent();

  // DEBUG: Log the AI response
  console.log("[YT-Bookmarker] DEBUG: AI response received:");
  console.log("Article length:", articleContent.length);
  console.log("Article preview:", articleContent.substring(0, 300));

  return articleContent;
}

// Export for both CommonJS (tests) and global scope (extension)
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    transformTranscriptToArticle
  };
} else {
  // Export for browser/extension environment
  if (typeof window !== 'undefined') {
    window.transformTranscriptToArticle = transformTranscriptToArticle;
  } else if (typeof globalThis !== 'undefined') {
    globalThis.transformTranscriptToArticle = transformTranscriptToArticle;
  }
}
