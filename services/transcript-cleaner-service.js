// Browser-compatible transcript cleaner service for Stage 1 of pipeline

async function cleanTranscript(rawTranscript) {
  if (
    !rawTranscript ||
    typeof rawTranscript !== "string" ||
    rawTranscript.trim() === ""
  ) {
    throw new Error("Raw transcript must be a non-empty string");
  }

  // DEBUG: Log the raw transcript being processed
  console.log("[YT-Bookmarker] DEBUG: Raw transcript being cleaned:");
  console.log("Length:", rawTranscript.length);
  console.log("First 300 chars:", rawTranscript.substring(0, 300));

  // Get API key from storage
  const result = await browser.storage.local.get("openai_api_key");
  const apiKey = result.openai_api_key;

  if (!apiKey) {
    throw new Error("OpenAI API key not configured");
  }

  const openai = new BrowserOpenAIService({ apiKey });

  // Get system prompt from shared templates
  const { SYSTEM_PROMPT } = window.TRANSCRIPT_CLEANER_PROMPTS;

  // Use the CSP-safe template service with precompiled templates
  if (typeof NunjucksService === 'undefined') {
    throw new Error("Template service not available - nunjucks-service.js may not have loaded");
  }

  const templateService = new NunjucksService();
  const userPrompt = templateService.renderTemplate('transcript-cleaner.njk', { transcript: rawTranscript });

  // DEBUG: Log the full prompt being sent
  console.log("[YT-Bookmarker] DEBUG: Transcript cleaner prompt being sent:");
  console.log("System prompt length:", SYSTEM_PROMPT.length);
  console.log("User prompt length:", userPrompt.length);

  const response = await openai.withSystem(SYSTEM_PROMPT, userPrompt, {
    model: "gpt-4o-mini",
    temperature: 0.3, // Lower temperature for more consistent cleaning
    max_tokens: 3000, // Allow more tokens for longer transcripts
  });

  const cleanedTranscript = response.getContent();

  // DEBUG: Log the AI response
  console.log("[YT-Bookmarker] DEBUG: Cleaned transcript received:");
  console.log("Cleaned length:", cleanedTranscript.length);
  console.log("Cleaned preview:", cleanedTranscript.substring(0, 300));

  return cleanedTranscript;
}

// Export for both CommonJS (tests) and global scope (extension)
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    cleanTranscript
  };
} else {
  // Export for browser/extension environment
  if (typeof window !== 'undefined') {
    window.cleanTranscript = cleanTranscript;
  } else if (typeof globalThis !== 'undefined') {
    globalThis.cleanTranscript = cleanTranscript;
  }
}