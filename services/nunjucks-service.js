// Browser-compatible Nunjucks template service for Firefox extensions
class NunjucksService {
  constructor() {
    this.env = null;
    this.usePrecompiled = true;
    this.initializeNunjucks();
  }

  initializeNunjucks() {
    try {
      // Check if Nunjucks slim is available globally
      if (typeof nunjucks === 'undefined') {
        throw new Error('Nunjucks library not loaded');
      }

      // Check if precompiled templates are available
      if (typeof window.nunjucksPrecompiled === 'undefined') {
        console.warn('[Nunjucks Service] Precompiled templates not found, falling back to string replacement');
        this.usePrecompiled = false;
        return;
      }

      // Configure Nunjucks for precompiled templates
      this.env = nunjucks.configure({
        autoescape: false,
        throwOnUndefined: false,
        trimBlocks: false,
        lstripBlocks: false
      });

      console.log('[Nunjucks Service] Successfully initialized with precompiled templates');
    } catch (error) {
      console.error('[Nunjucks Service] Initialization failed:', error);
      this.usePrecompiled = false;
    }
  }

  /**
   * Render a template with variables using precompiled templates
   * @param {string} templateName - Name of precompiled template (e.g. 'title-response.njk')
   * @param {object} variables - Object with variable values
   * @returns {string} Rendered template
   */
  renderTemplate(templateName, variables = {}) {
    if (this.usePrecompiled && this.env) {
      try {
        const result = nunjucks.render(templateName, variables);
        
        console.log('[Nunjucks Service] Precompiled template rendered successfully:', {
          templateName,
          variableCount: Object.keys(variables).length,
          resultLength: result.length
        });

        return result;
      } catch (error) {
        console.error('[Nunjucks Service] Precompiled render error:', error);
      }
    }
    
    // Fallback to string replacement if precompiled fails
    console.warn('[Nunjucks Service] Falling back to string replacement for template:', templateName);
    return this.getTemplateStringAndRender(templateName, variables);
  }

  /**
   * Render a template string with variables (legacy method for backwards compatibility)
   * @param {string} templateString - Template with {{ variable }} syntax
   * @param {object} variables - Object with variable values
   * @returns {string} Rendered template
   */
  renderString(templateString, variables = {}) {
    if (this.usePrecompiled && this.env) {
      try {
        // Try using renderString with precompiled environment
        const result = nunjucks.renderString(templateString, variables);
        
        console.log('[Nunjucks Service] Template string rendered successfully:', {
          templateLength: templateString.length,
          variableCount: Object.keys(variables).length,
          resultLength: result.length
        });

        return result;
      } catch (error) {
        console.error('[Nunjucks Service] Render error:', error);
      }
    }
    
    // Fallback to simple string replacement if Nunjucks fails
    console.warn('[Nunjucks Service] Falling back to string replacement');
    return this.fallbackStringReplacement(templateString, variables);
  }

  /**
   * Get template string for fallback rendering
   * @param {string} templateName - Template name
   * @param {object} variables - Variables
   * @returns {string} Rendered string
   */
  getTemplateStringAndRender(templateName, variables = {}) {
    // Map template names to template strings for fallback
    const templateStrings = {
      'transcript-cleaner.njk': `Please clean up this raw YouTube transcript. Remove filler words, fix sentence structure, and organize into readable paragraphs, but preserve every detail, fact, claim, and piece of information exactly as stated.

Raw Transcript:
---
{{ transcript }}
---

Return the cleaned transcript as flowing, readable text:`,
      'title-response.njk': `VIDEO TITLE: {{ title }}

TRANSCRIPT: {{ transcript }}

Generate a Title Response - a single sentence that directly answers the question or promise made by this video title. Focus on the concrete outcome, result, or answer revealed in the video.

Title Response:`,
      'investigative-reporter.njk': `Transform this YouTube transcript into a news story that completes a clickbait title, then provides dense technical coverage.

Requirements:
- Write the first sentence assuming a clickbait title was just read - it should complete the thought naturally
- First sentence (20-40 words) delivers the real story with key limitation/reality check
- Follow with dense technical narrative (100-150 words) covering implementation, benchmarks, comparisons
- Include EVERY number, specification, and technical detail from transcript
- Use specific comparisons (IBM's 2ms vs their 10ms)
- Must sound natural for TV news anchor throughout
- Note what's technically suspicious or missing

# Example flow:
---
title: "Run a FREE ChatGPT type app on Your PC!":
output: "Use Ollama to install models like Llama locally, but expect slower performance and you'll need decent GPU power to make it actually useful!" [THEN CONTINUE WITH FULL TECHNICAL STORY...]

title: "This One Weird AI Trick Is Changing Everything About How Computers Think.":
output: "RoPE (Rotational Position Embedding) was recently invented by Meta to use rotating vectors in mathematical space in addition to standard position numbers to improve LLMs' understanding of relationships between different words on long texts." [THEN CONTINUE WITH FULL TECHNICAL STORY...]

The first sentence should seamlessly continue whatever clickbait title precedes it.

Transcript:
---
{{ transcript }}
---

Write the completion sentence and full technical breakdown that would follow any relevant clickbait title.`
    };

    const templateString = templateStrings[templateName];
    if (!templateString) {
      console.error('[Nunjucks Service] Unknown template:', templateName);
      return '';
    }

    return this.fallbackStringReplacement(templateString, variables);
  }

  /**
   * Fallback string replacement method
   * @param {string} templateString - Template string
   * @param {object} variables - Variables to replace
   * @returns {string} Rendered string
   */
  fallbackStringReplacement(templateString, variables = {}) {
    let result = templateString;
    
    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`\\{\\{\\s*${this.escapeRegex(key)}\\s*\\}\\}`, 'g');
      const replacement = value != null ? String(value) : '';
      result = result.replace(regex, replacement);
    }
    
    return result;
  }

  /**
   * Escape special regex characters
   * @param {string} string - String to escape
   * @returns {string} Escaped string
   */
  escapeRegex(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }
}

// Export for both CommonJS (tests) and global scope (extension)
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { NunjucksService };
} else {
  // For browser/extension environment
  if (typeof window !== 'undefined') {
    window.NunjucksService = NunjucksService;
  } else if (typeof globalThis !== 'undefined') {
    globalThis.NunjucksService = NunjucksService;
  }
}