// Browser-compatible OpenAI service for Firefox extension

class BrowserOpenAIService {
  constructor(config = {}) {
    this.config = {
      apiKey: config.apiKey || null,
      baseUrl: config.baseUrl || "https://api.openai.com/v1",
      defaultModel: config.defaultModel || "gpt-4.1",
      timeout: config.timeout || 60000,
    };
  }

  async withSystem(systemPrompt, userPrompt, options = {}) {
    const messages = [
      { role: "system", content: systemPrompt },
      { role: "user", content: userPrompt },
    ];

    const response = await this.chat(messages, options);
    return response;
  }

  async chat(messages, options = {}) {
    if (!this.config.apiKey) {
      throw new Error("API key is required");
    }

    const requestBody = {
      model: options.model || this.config.defaultModel,
      messages: messages,
      max_tokens: options.max_tokens || 2000,
      temperature: options.temperature || 0.7,
      top_p: options.top_p || 1,
      frequency_penalty: options.frequency_penalty || 0,
      presence_penalty: options.presence_penalty || 0,
    };

    const url = `${this.config.baseUrl}/chat/completions`;

    try {
      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${this.config.apiKey}`,
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage =
          errorData.error?.message ||
          `HTTP ${response.status}: ${response.statusText}`;
        throw new Error(errorMessage);
      }

      const responseData = await response.json();
      return new BrowserCompletionResponse(responseData);
    } catch (error) {
      throw new Error(`OpenAI API request failed: ${error.message}`);
    }
  }
}

class BrowserCompletionResponse {
  constructor(responseData) {
    this.data = responseData;
    this.choices = responseData.choices || [];
  }

  getContent() {
    if (this.choices.length === 0) {
      throw new Error("No choices available in response");
    }
    return this.choices[0].message?.content || "";
  }

  getUsage() {
    return this.data.usage;
  }
}

// Export for both CommonJS (tests) and global scope (extension)
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    BrowserOpenAIService
  };
} else {
  // Export for browser/extension environment
  if (typeof window !== 'undefined') {
    window.BrowserOpenAIService = BrowserOpenAIService;
  } else if (typeof globalThis !== 'undefined') {
    globalThis.BrowserOpenAIService = BrowserOpenAIService;
  }
}
