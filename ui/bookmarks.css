body {
    margin: 0;
    padding: 0;
    font-family: Arial, sans-serif;
    background-color: #f9f9f9;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: flex-start;
}

.page-container {
    width: 100%;
    max-width: 800px;
    margin: 40px auto;
    padding: 0 20px;
}

.header {
    background-color: #fff;
    padding: 15px;
    border-bottom: 1px solid #ddd;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border-radius: 5px 5px 0 0;
}

.header h2 {
    margin: 0;
    color: #333;
    font-size: 24px;
    text-align: center;
}

#header-title {
    cursor: default;
    position: relative;
}

#header-title .default-text {
    display: none;
}

#video-list-container {
    padding: 20px;
    background-color: white;
    border-left: 1px solid #ddd;
    border-right: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
    border-radius: 0 0 5px 5px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.empty-message {
    text-align: center;
    color: #888;
    font-style: italic;
    margin: 30px 10px;
    line-height: 1.4;
    font-size: 16px;
}

.video-item {
    background-color: #f8f8f8;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    margin-bottom: 15px;
    padding: 15px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    transition: box-shadow 0.2s;
}

.video-item:hover {
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}

.content-wrapper {
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.thumbnail {
    width: 120px;
    height: 90px;
    object-fit: cover;
    border-radius: 5px;
    flex-shrink: 0;
}

.thumbnail.clickable {
    cursor: pointer;
    transition: opacity 0.2s;
}

.thumbnail.clickable:hover {
    opacity: 0.8;
}

.info {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
}

.title {
    margin: 0 0 8px 0;
    font-weight: bold;
    font-size: 16px;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.title.clickable {
    cursor: pointer;
    color: #1976d2;
    transition: color 0.2s;
}

.title.clickable:hover {
    color: #1565c0;
    text-decoration: underline;
}

.channel {
    margin: 4px 0 8px 0;
    font-size: 13px;
    color: #666;
    font-weight: 500;
}

.date {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #666;
}

.article-preview {
    margin: 0 0 12px 0;
    font-size: 14px;
    color: #333;
    line-height: 1.5;
    background-color: #f8f9fa;
    padding: 12px;
    border-radius: 4px;
    border-left: 3px solid #1976d2;
}

.article-preview.loading {
    color: #ff9800;
    font-style: italic;
    background-color: #fff3e0;
    border-left-color: #ff9800;
}

.article-preview.error {
    color: #999;
    font-style: italic;
    background-color: #f5f5f5;
    border-left-color: #999;
}

.actions {
    display: flex;
    flex-direction: row;
    gap: 8px;
    margin-top: auto;
    padding-top: 10px;
}

.transcript-btn, .delete-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    font-weight: bold;
    transition: background-color 0.2s;
    white-space: nowrap;
}

.transcript-btn {
    background-color: #1976d2;
    color: white;
}

.transcript-btn:hover {
    background-color: #1565c0;
}

/* Specific styles for different transcript states */
.transcript-fetching {
    background-color: #ff9800 !important;
    cursor: default;
}

.transcript-fetching:hover {
    background-color: #f57c00 !important;
}

.transcript-ready {
    background-color: #4caf50 !important;
}

.transcript-ready:hover {
    background-color: #45a049 !important;
}

.transcript-error {
    background-color: #f44336 !important;
}

.transcript-error:hover {
    background-color: #da190b !important;
}

.delete-btn {
    background-color: #f44336;
    color: white;
}

.delete-btn:hover {
    background-color: #da190b;
}