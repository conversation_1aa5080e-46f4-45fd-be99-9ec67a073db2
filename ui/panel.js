document.addEventListener("DOMContentLoaded", () => {
  console.log("Panel DOM content loaded.");
  const videoListContainer = document.getElementById("video-list-container");
  
  // Check API key status and show setup option if needed
  checkApiKeyStatus();
  
  // Add click handler for header to open in new tab
  const headerTitle = document.getElementById("header-title");
  if (headerTitle) {
    headerTitle.addEventListener("click", async () => {
      try {
        await browser.runtime.sendMessage({ action: "openInTab" });
        // Close the popup after opening in tab
        window.close();
      } catch (error) {
        console.error("[YT-Bookmarker] Error opening in tab:", error);
      }
    });
  }

  // Helper function to get transcript button text based on status
  function getTranscriptButtonText(status) {
    switch (status) {
      case "fetching":
        return "Loading...";
      case "ready":
        return "Transcript";
      case "error":
        return "Retry";
      default:
        return "Loading...";
    }
  }

  function render(videos) {
    console.log("Rendering videos:", videos);
    videoListContainer.innerHTML = "";

    if (!videos || videos.length === 0) {
      videoListContainer.innerHTML =
        '<p class="empty-message">No videos bookmarked yet. Right-click on YouTube videos to bookmark them!</p>';
      return;
    }

    videos.forEach((video) => {
      const videoItem = document.createElement("div");
      videoItem.className = "video-item";

      // Format date for tag
      const addedDate = new Date(video.dateAdded).toLocaleDateString();
      
      videoItem.innerHTML = `
                <div class="thumbnail-container">
                    <img src="${
                      video.thumbnailUrl
                    }" class="thumbnail clickable" alt="Video thumbnail" data-url="${
        video.url
      }">
                    ${video.duration ? `<div class="duration-overlay">${video.duration}</div>` : ''}
                </div>
                <div class="info">
                    <p class="title clickable" data-url="${video.url}" title="${video.title}">${
        video.title
      }</p>
                    ${video.titleResponse ? `<div class="title-response">${video.titleResponse}</div>` : ''}
                    ${video.channelName ? `<p class="channel">${video.channelName}</p>` : ''}
                    <div class="metadata-row">
                        ${video.viewCount ? `<span class="views">${video.viewCount}</span>` : ''}
                        ${video.viewCount && video.uploadTime ? '<span class="separator"> • </span>' : ''}
                        ${video.uploadTime ? `<span class="upload-time">${video.uploadTime}</span>` : ''}
                    </div>
                    <div class="tag-container">
                        <span class="added-tag">Added: ${addedDate}</span>
                    </div>
                </div>
                <div class="actions">
                    <button class="transcript-btn transcript-${
                      video.transcriptStatus || 'fetching'
                    }" data-id="${
                      video.id
                    }" data-title="${video.title}" data-status="${
                      video.transcriptStatus || 'fetching'
                    }">${getTranscriptButtonText(video.transcriptStatus)}</button>
                    <button class="delete-btn" data-id="${
                      video.id
                    }">Delete</button>
                </div>
            `;

      videoListContainer.appendChild(videoItem);
    });

    // Add event listeners for clickable elements (thumbnails and titles)
    document.querySelectorAll(".clickable").forEach((element) => {
      element.addEventListener("click", (e) => {
        const url = e.target.dataset.url;
        browser.tabs.create({ url: url });
        window.close(); // Close the popup
      });
    });

    // Add event listeners for transcript buttons
    document.querySelectorAll(".transcript-btn").forEach((btn) => {
      btn.addEventListener("click", async (e) => {
        const videoId = e.target.dataset.id;
        const videoTitle = e.target.dataset.title;
        await getTranscript(videoId, videoTitle, e.target);
      });
    });

    document.querySelectorAll(".delete-btn").forEach((btn) => {
      btn.addEventListener("click", async (e) => {
        const videoId = e.target.dataset.id;
        await deleteVideo(videoId);
      });
    });
  }

  async function deleteVideo(videoId) {
    try {
      const result = await browser.storage.local.get("videos");
      const videos = result.videos || [];
      const updatedVideos = videos.filter((video) => video.id !== videoId);
      await browser.storage.local.set({ videos: updatedVideos });
      console.log(`Deleted video ${videoId}`);
    } catch (error) {
      console.error("Error deleting video:", error);
    }
  }

  async function getTranscript(videoId, videoTitle, button) {
    const originalText = button.textContent;
    const status = button.dataset.status;
    
    try {
      // Handle different statuses
      if (status === "fetching") {
        // Still fetching, show message
        button.textContent = "Please wait...";
        button.style.backgroundColor = "orange";
        setTimeout(() => {
          button.textContent = originalText;
          button.style.backgroundColor = "";
        }, 2000);
        return;
      }
      
      if (status === "error") {
        // Retry fetching - use old getTranscript action for retry
        button.textContent = "Retrying...";
        button.style.backgroundColor = "orange";
        button.disabled = true;
        
        const response = await browser.runtime.sendMessage({
          action: "getTranscript",
          videoId: videoId,
          videoTitle: videoTitle,
        });
        
        if (response?.success !== false) {
          button.textContent = "Success!";
          button.style.backgroundColor = "green";
          setTimeout(() => {
            window.close();
          }, 1000);
        } else {
          throw new Error(response?.error || "Retry failed");
        }
        return;
      }

      // For "ready" status or default, show transcript immediately
      button.textContent = "Opening...";
      button.style.backgroundColor = "#007bff";
      button.disabled = true;

      // Send message to background script for immediate display
      const response = await browser.runtime.sendMessage({
        action: "showTranscript",
        videoId: videoId,
        videoTitle: videoTitle,
      });
      
      // Debug log the actual response for troubleshooting
      console.log("[YT-Summarizer] showTranscript response:", JSON.stringify(response, null, 2));

      // Handle response more robustly
      if (response?.success === true) {
        button.textContent = "Opened!";
        button.style.backgroundColor = "green";
        // Close popup after background script completes
        setTimeout(() => {
          window.close();
        }, 600);
        return; // Exit function successfully
      }
      
      // Handle different status cases more explicitly
      const responseStatus = response?.status;
      switch (responseStatus) {
        case "fetching":
          button.textContent = "Still loading...";
          button.style.backgroundColor = "orange";
          setTimeout(() => {
            button.textContent = originalText;
            button.style.backgroundColor = "";
            button.disabled = false;
          }, 2000);
          return; // Exit function successfully
          
        case "error":
          throw new Error(response?.message || "Transcript error");
          
        case "ready":
          // This should have been handled by success case above, but handle gracefully
          button.textContent = "Opened!";
          button.style.backgroundColor = "green";
          setTimeout(() => {
            window.close();
          }, 600);
          return;
          
        case "unknown":
        case null:
        case undefined:
        default:
          // Handle unknown/missing status gracefully
          console.warn("[YT-Summarizer] Unexpected response status:", responseStatus, "Full response:", response);
          button.textContent = "Retry";
          button.style.backgroundColor = "orange";
          setTimeout(() => {
            button.textContent = originalText;
            button.style.backgroundColor = "";
            button.disabled = false;
          }, 2000);
          return; // Don't throw error, just let user retry
      }
      
    } catch (error) {
      console.error("[YT-Summarizer] Error requesting transcript:", error);
      
      // Error feedback
      button.textContent = "Error!";
      button.style.backgroundColor = "red";
      button.disabled = false;
      
      setTimeout(() => {
        button.textContent = originalText;
        button.style.backgroundColor = "";
      }, 3000);
    }
  }

  function loadAndRenderVideos() {
    console.log("Loading and rendering videos from storage.");
    browser.storage.local.get("videos").then(
      (result) => {
        console.log("Loaded videos from storage:", result.videos);
        const videos = result.videos || [];
        render(videos);
      },
      (error) => {
        console.error("Error loading videos from storage:", error);
      },
    );
  }

  browser.storage.onChanged.addListener((changes, area) => {
    console.log("Storage changed in area:", area);
    if (area === "local" && changes.videos) {
      console.log(
        "'videos' in storage changed. New value:",
        changes.videos.newValue,
      );
      render(changes.videos.newValue || []);
    }
  });

  loadAndRenderVideos();
  
  // Function to check API keys status and show setup option
  async function checkApiKeyStatus() {
    try {
      const result = await browser.storage.local.get(['apify_api_key', 'openai_api_key', 'api_keys_configured']);
      
      const hasApifyKey = !!result.apify_api_key;
      const hasOpenAIKey = !!result.openai_api_key;
      const hasAllKeys = hasApifyKey && hasOpenAIKey;
      
      if (!hasAllKeys) {
        // Add API key setup button to the popup
        addApiKeySetupButton(hasApifyKey, hasOpenAIKey);
      }
    } catch (error) {
      console.error('[YT-Bookmarker] Error checking API keys status:', error);
    }
  }
  
  function addApiKeySetupButton(hasApifyKey, hasOpenAIKey) {
    // Check if button already exists
    if (document.getElementById('api-key-setup-btn')) return;
    
    const container = document.querySelector('.header') || videoListContainer.parentElement;
    
    // Create appropriate message based on missing keys
    let message = '⚠️ Setup API keys: ';
    const missing = [];
    if (!hasApifyKey) missing.push('Apify (transcripts)');
    if (!hasOpenAIKey) missing.push('OpenAI (articles)');
    message += missing.join(' & ');
    
    const setupButton = document.createElement('div');
    setupButton.innerHTML = `
      <div id="api-key-setup-btn" style="
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 4px;
        padding: 8px 12px;
        margin: 10px 0;
        text-align: center;
        cursor: pointer;
        transition: background-color 0.2s;
      ">
        <small style="color: #856404;">${message}</small>
      </div>
    `;
    
    const button = setupButton.firstElementChild;
    button.addEventListener('click', async () => {
      try {
        // Use background script to handle tab deduplication
        await browser.runtime.sendMessage({ action: "openApiSetup" });
        window.close();
      } catch (error) {
        console.error('[YT-Bookmarker] Error opening API key setup:', error);
      }
    });
    
    button.addEventListener('mouseenter', () => {
      button.style.backgroundColor = '#fff3cd';
    });
    
    button.addEventListener('mouseleave', () => {
      button.style.backgroundColor = '#fff3cd';
    });
    
    container.appendChild(setupButton);
  }
});

