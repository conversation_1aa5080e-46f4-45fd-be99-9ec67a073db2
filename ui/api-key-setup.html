<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Key Setup - YouTube Bookmarker</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 400px;
            margin: 0;
            padding: 20px;
            background-color: #f9f9f9;
            line-height: 1.5;
        }
        
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        h2 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 20px;
        }
        
        p {
            color: #666;
            margin: 0 0 15px 0;
            font-size: 14px;
        }
        
        .input-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
        
        input[type="password"] {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        input[type="password"]:focus {
            outline: none;
            border-color: #1976d2;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        
        button {
            flex: 1;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .primary-btn {
            background-color: #1976d2;
            color: white;
        }
        
        .primary-btn:hover {
            background-color: #1565c0;
        }
        
        .primary-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        
        .secondary-btn {
            background-color: #f5f5f5;
            color: #333;
            border: 1px solid #ddd;
        }
        
        .secondary-btn:hover {
            background-color: #e9e9e9;
        }
        
        .help-text {
            font-size: 12px;
            color: #888;
            margin-top: 5px;
        }
        
        .error-message {
            color: #d32f2f;
            font-size: 12px;
            margin-top: 5px;
            display: none;
        }
        
        .success-message {
            color: #2e7d32;
            font-size: 12px;
            margin-top: 5px;
            display: none;
        }
        
        .info-box {
            background-color: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 15px;
            font-size: 12px;
            color: #1565c0;
        }
        
        .info-box a {
            color: #1565c0;
            text-decoration: none;
        }
        
        .info-box a:hover {
            text-decoration: underline;
        }
        
        .env-import {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.5;
            resize: vertical;
            box-sizing: border-box;
            min-height: 80px;
        }
        
        .env-import:focus {
            outline: none;
            border-color: #1976d2;
        }
        
        .env-import-container {
            display: none;
        }
        
        .env-import-toggle {
            cursor: pointer;
            background-color: #f8f9fa;
            border: 1px solid #1976d2;
            border-radius: 6px;
            padding: 12px 20px;
            margin: 15px 0;
            text-align: center;
            color: #1976d2;
            font-weight: 500;
            font-size: 14px;
            transition: all 0.2s ease;
            user-select: none;
        }
        
        .env-import-toggle:hover {
            background-color: #e3f2fd;
            border-color: #1565c0;
            color: #1565c0;
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>🔑 API Keys Required</h2>
        
        <p>YouTube Bookmarker needs API keys to extract transcripts and generate article summaries and title responses.</p>
        
        <div class="info-box">
            <strong>Required API Keys:</strong><br>
            • <strong>Apify:</strong> For extracting YouTube transcripts<br>
            • <strong>OpenAI:</strong> For generating article summaries and title responses<br>
            • <strong>DeepSeek:</strong> For future AI features<br><br>
            <strong>How to get them:</strong><br>
            1. <a href="https://console.apify.com/account/integrations" target="_blank">Apify API Token</a> - Sign up and get your token<br>
            2. <a href="https://platform.openai.com/api-keys" target="_blank">OpenAI API Key</a> - Create account and generate key<br>
            3. <a href="https://platform.deepseek.com/api_keys" target="_blank">DeepSeek API Key</a> - Create DeepSeek account and generate key
        </div>
        
        <form id="api-key-form">
            <div class="input-group">
                <label for="apify-key">Apify API Token:</label>
                <input type="password" id="apify-key" placeholder="apify_api_..." required>
                <div class="help-text">Required for transcript extraction from YouTube videos.</div>
                <div id="apify-error" class="error-message"></div>
            </div>
            
            <div class="input-group">
                <label for="openai-key">OpenAI API Key:</label>
                <input type="password" id="openai-key" placeholder="sk-..." required>
                <div class="help-text">Required for generating article summaries.</div>
                <div id="openai-error" class="error-message"></div>
            </div>
            
            <div class="input-group">
                <label for="deepseek-key">DeepSeek API Key:</label>
                <input type="password" id="deepseek-key" placeholder="sk-..." required>
                <div class="help-text">For future AI features and advanced processing.</div>
                <div id="deepseek-error" class="error-message"></div>
            </div>
            
            <div class="env-import-toggle" id="env-import-toggle">💾 Import from .env file</div>
            <div class="input-group env-import-container" id="env-import-container">
                <textarea id="env-import" class="env-import" placeholder="Paste .env content here:&#10;APIFY_API_KEY=apify_api_...&#10;OPENAI_API_KEY=sk-...&#10;DEEPSEEK_API_KEY=sk-..." rows="3"></textarea>
            </div>
            
            <div class="help-text" style="margin-top: 10px; text-align: center;">
                Your API keys are stored locally and never shared.
            </div>
            <div id="success-message" class="success-message">API keys saved successfully!</div>
            
            <div class="button-group">
                <button type="button" id="skip-btn" class="secondary-btn">Skip for Now</button>
                <button type="submit" id="save-btn" class="primary-btn">Save Keys</button>
            </div>
        </form>
    </div>

    <script src="api-key-setup.js"></script>
</body>
</html>