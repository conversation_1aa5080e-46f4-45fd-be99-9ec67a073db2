document.addEventListener("DOMContentLoaded", () => {
  console.log("Bookmarks page DOM content loaded.");
  const videoListContainer = document.getElementById("video-list-container");

  // Helper function to get transcript button text based on status
  function getTranscriptButtonText(status) {
    switch (status) {
      case "fetching":
        return "Loading...";
      case "ready":
        return "Listen";
      case "error":
        return "Retry";
      default:
        return "Loading...";
    }
  }

  // Helper function to convert markdown to HTML
  function markdownToHtml(markdown) {
    if (!markdown || typeof markdown !== 'string') {
      return '';
    }
    
    let html = markdown
      // Escape any existing HTML to prevent XSS
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      
      // Headers (## Header -> <h2>Header</h2>)
      .replace(/^### (.*$)/gm, '<h3>$1</h3>')
      .replace(/^## (.*$)/gm, '<h2>$1</h2>')
      .replace(/^# (.*$)/gm, '<h1>$1</h1>')
      
      // Bold text (**text** -> <strong>text</strong>)
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      
      // Italic text (*text* -> <em>text</em>)
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      
      // Inline code (`code` -> <code>code</code>)
      .replace(/`([^`]+)`/g, '<code>$1</code>')
      
      // Links ([text](url) -> <a href="url">text</a>)
      .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>')
      
      // Convert double newlines to paragraphs
      .replace(/\n\n/g, '</p><p>')
      
      // Convert single newlines to line breaks
      .replace(/\n/g, '<br>');
    
    // Handle unordered lists (- item -> <ul><li>item</li></ul>)
    html = html.replace(/^- (.+)$/gm, '<li>$1</li>');
    html = html.replace(/(<li>.*<\/li>)/gs, '<ul>$1</ul>');
    
    // Handle numbered lists (1. item -> <ol><li>item</li></ol>)
    html = html.replace(/^\d+\. (.+)$/gm, '<li>$1</li>');
    html = html.replace(/(<li>.*<\/li>)/gs, (match) => {
      if (!match.includes('<ul>')) {
        return `<ol>${match}</ol>`;
      }
      return match;
    });
    
    // Wrap in paragraphs if not already wrapped
    if (!html.startsWith('<')) {
      html = `<p>${html}</p>`;
    }
    
    return html;
  }

  // Helper function to get article preview
  function getArticlePreview(video) {
    if (video.transcriptStatus !== "ready" || !video.article) {
      if (video.transcriptStatus === "fetching") {
        return '<span class="article-preview loading">Generating article...</span>';
      } else if (video.transcriptStatus === "error") {
        return '<span class="article-preview error">Article unavailable</span>';
      }
      return "";
    }
    
    const article = video.article.trim();
    const htmlContent = markdownToHtml(article);
    return `<div class="article-preview">${htmlContent}</div>`;
  }

  function render(videos) {
    console.log("Rendering videos:", videos);
    videoListContainer.innerHTML = "";

    if (!videos || videos.length === 0) {
      videoListContainer.innerHTML =
        '<p class="empty-message">No videos bookmarked yet. Right-click on YouTube videos to bookmark them!</p>';
      return;
    }

    videos.forEach((video) => {
      const videoItem = document.createElement("div");
      videoItem.className = "video-item";

      videoItem.innerHTML = `
                <div class="content-wrapper">
                    <div class="info">
                        <p class="title clickable" data-url="${video.url}">${
        video.title
      }</p>
                        ${video.channelName ? `<p class="channel">${video.channelName}</p>` : ''}
                        <p class="date">Added: ${new Date(
                          video.dateAdded,
                        ).toLocaleDateString()}</p>
                        ${getArticlePreview(video)}
                        <div class="actions">
                            <button class="transcript-btn transcript-${
                              video.transcriptStatus || 'fetching'
                            }" data-id="${
                              video.id
                            }" data-title="${video.title}" data-status="${
                              video.transcriptStatus || 'fetching'
                            }">${getTranscriptButtonText(video.transcriptStatus)}</button>
                            <button class="delete-btn" data-id="${
                              video.id
                            }">Delete</button>
                        </div>
                    </div>
                    <img src="${
                      video.thumbnailUrl
                    }" class="thumbnail clickable" alt="Video thumbnail" data-url="${
        video.url
      }">
                </div>
            `;

      videoListContainer.appendChild(videoItem);
    });

    // Add event listeners for clickable elements (thumbnails and titles)
    document.querySelectorAll(".clickable").forEach((element) => {
      element.addEventListener("click", (e) => {
        const url = e.target.dataset.url;
        browser.tabs.create({ url: url });
      });
    });

    // Add event listeners for transcript buttons
    document.querySelectorAll(".transcript-btn").forEach((btn) => {
      btn.addEventListener("click", async (e) => {
        const videoId = e.target.dataset.id;
        const videoTitle = e.target.dataset.title;
        await playTranscriptAudio(videoId, videoTitle, e.target);
      });
    });

    document.querySelectorAll(".delete-btn").forEach((btn) => {
      btn.addEventListener("click", async (e) => {
        const videoId = e.target.dataset.id;
        await deleteVideo(videoId);
      });
    });
  }

  async function deleteVideo(videoId) {
    try {
      const result = await browser.storage.local.get("videos");
      const videos = result.videos || [];
      const updatedVideos = videos.filter((video) => video.id !== videoId);
      await browser.storage.local.set({ videos: updatedVideos });
      console.log(`Deleted video ${videoId}`);
    } catch (error) {
      console.error("Error deleting video:", error);
    }
  }

  async function playTranscriptAudio(videoId, videoTitle, button) {
    const originalText = button.textContent;
    const status = button.dataset.status;
    
    try {
      // Handle different statuses
      if (status === "fetching") {
        // Still fetching transcript, show message
        button.textContent = "Please wait...";
        button.style.backgroundColor = "orange";
        setTimeout(() => {
          button.textContent = originalText;
          button.style.backgroundColor = "";
        }, 2000);
        return;
      }
      
      if (status === "error") {
        // Retry fetching transcript first
        button.textContent = "Retrying...";
        button.style.backgroundColor = "orange";
        button.disabled = true;
        
        const response = await browser.runtime.sendMessage({
          action: "getTranscript",
          videoId: videoId,
          videoTitle: videoTitle,
        });
        
        if (response?.success !== false) {
          button.textContent = "Success!";
          button.style.backgroundColor = "green";
          // Reload the page to show updated transcript status
          setTimeout(() => {
            location.reload();
          }, 1000);
        } else {
          throw new Error(response?.error || "Retry failed");
        }
        return;
      }

      // For "ready" status, check if audio is available
      const result = await browser.storage.local.get("videos");
      const videos = result.videos || [];
      const video = videos.find(v => v.id === videoId);
      
      if (!video) {
        throw new Error("Video not found in storage");
      }
      
      if (!video.audioData) {
        // No pre-generated audio, generate it now
        button.textContent = "Generating...";
        button.style.backgroundColor = "#ff9800";
        button.disabled = true;
        
        const response = await browser.runtime.sendMessage({
          action: "generateAudioForVideo",
          videoId: videoId,
          videoTitle: video.title,
          transcript: video.transcript,
          article: video.article
        });
        
        if (!response.success) {
          throw new Error(response.error || "Failed to generate audio");
        }
        
        // Reload to get the updated audio data
        setTimeout(() => location.reload(), 500);
        return;
      }
      
      // Play the pre-generated audio
      button.textContent = "Playing...";
      button.style.backgroundColor = "green";
      button.disabled = true;
      
      // Convert base64 PCM to WAV
      const pcmData = Uint8Array.from(atob(video.audioData), c => c.charCodeAt(0));
      const wavData = pcmToWav(pcmData);
      const audioBlob = new Blob([wavData], { type: 'audio/wav' });
      const audioUrl = URL.createObjectURL(audioBlob);
      
      const audio = new Audio(audioUrl);
      
      audio.addEventListener('ended', () => {
        URL.revokeObjectURL(audioUrl);
        button.textContent = originalText;
        button.style.backgroundColor = "";
        button.disabled = false;
        console.log("[YT-Summarizer] Audio playback completed");
      });
      
      audio.addEventListener('error', (e) => {
        URL.revokeObjectURL(audioUrl);
        button.textContent = "Audio Error!";
        button.style.backgroundColor = "red";
        button.disabled = false;
        console.error("[YT-Summarizer] Audio playback error:", e);
        
        setTimeout(() => {
          button.textContent = originalText;
          button.style.backgroundColor = "";
        }, 3000);
      });
      
      await audio.play();
      console.log("[YT-Summarizer] Audio playback started");
      
    } catch (error) {
      console.error("[YT-Summarizer] Error generating audio:", error);
      
      // Error feedback
      button.textContent = "Error!";
      button.style.backgroundColor = "red";
      button.disabled = false;
      
      setTimeout(() => {
        button.textContent = originalText;
        button.style.backgroundColor = "";
      }, 3000);
    }
  }

  function loadAndRenderVideos() {
    console.log("Loading and rendering videos from storage.");
    browser.storage.local.get("videos").then(
      (result) => {
        console.log("Loaded videos from storage:", result.videos);
        const videos = result.videos || [];
        render(videos);
      },
      (error) => {
        console.error("Error loading videos from storage:", error);
      },
    );
  }

  browser.storage.onChanged.addListener((changes, area) => {
    console.log("Storage changed in area:", area);
    if (area === "local" && changes.videos) {
      console.log(
        "'videos' in storage changed. New value:",
        changes.videos.newValue,
      );
      render(changes.videos.newValue || []);
    }
  });

  loadAndRenderVideos();
});

// Helper function to convert PCM to WAV format
function pcmToWav(pcmData, sampleRate = 24000, numChannels = 1, bitsPerSample = 16) {
  const byteRate = sampleRate * numChannels * (bitsPerSample / 8);
  const blockAlign = numChannels * (bitsPerSample / 8);
  const dataSize = pcmData.length;
  
  const buffer = new ArrayBuffer(44 + dataSize);
  const view = new DataView(buffer);
  
  // WAV header
  const writeString = (offset, string) => {
    for (let i = 0; i < string.length; i++) {
      view.setUint8(offset + i, string.charCodeAt(i));
    }
  };
  
  writeString(0, 'RIFF');
  view.setUint32(4, 36 + dataSize, true);
  writeString(8, 'WAVE');
  writeString(12, 'fmt ');
  view.setUint32(16, 16, true); // fmt chunk size
  view.setUint16(20, 1, true); // audio format (1 = PCM)
  view.setUint16(22, numChannels, true);
  view.setUint32(24, sampleRate, true);
  view.setUint32(28, byteRate, true);
  view.setUint16(32, blockAlign, true);
  view.setUint16(34, bitsPerSample, true);
  writeString(36, 'data');
  view.setUint32(40, dataSize, true);
  
  // Copy PCM data
  const wavData = new Uint8Array(buffer);
  wavData.set(pcmData, 44);
  
  return wavData;
}