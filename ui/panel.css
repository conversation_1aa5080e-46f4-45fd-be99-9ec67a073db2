body {
  width: 400px;
  min-height: 200px;
  margin: 0;
  padding: 0;
  font-family: Arial, sans-serif;
  background-color: #f9f9f9;
}

.header {
  background-color: #fff;
  padding: 15px;
  border-bottom: 1px solid #ddd;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header h2 {
  margin: 0;
  color: #333;
  font-size: 18px;
  text-align: center;
}

#header-title {
  cursor: pointer;
  position: relative;
}

#header-title * {
  cursor: pointer;
}

#header-title .default-text {
  display: none;
}

#header-title:hover .youtube-logo {
  opacity: 0.7;
}

#header-title .youtube-logo {
  transition: opacity 0.2s ease;
}

#video-list-container {
  padding: 10px;
  max-height: 500px;
  overflow-y: auto;
}

.empty-message {
  text-align: center;
  color: #888;
  font-style: italic;
  margin: 30px 10px;
  line-height: 1.4;
}

.video-item {
  display: flex;
  align-items: flex-start;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 5px;
  margin-bottom: 10px;
  padding: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.thumbnail-container {
  position: relative;
  width: 100px;
  height: 75px;
  margin-right: 10px;
  flex-shrink: 0;
}

.thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 3px;
}

.thumbnail.clickable {
  cursor: pointer;
  transition: opacity 0.2s;
}

.thumbnail.clickable:hover {
  opacity: 0.8;
}

.duration-overlay {
  position: absolute;
  bottom: 4px;
  right: 4px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 2px 4px;
  border-radius: 2px;
  font-size: 11px;
  font-weight: 500;
  line-height: 1;
}

.info {
  flex: 1;
  min-width: 0;
}

.title {
  margin: 0 0 5px 0;
  font-weight: bold;
  font-size: 14px;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.title.clickable {
  cursor: pointer;
  color: #1976d2;
  transition: color 0.2s;
}

.title.clickable:hover {
  color: #1565c0;
  text-decoration: underline;
}

.title-response {
  margin: 5px 0 8px 0;
  font-size: 13px;
  line-height: 1.4;
  color: #333;
  background: #f0f7ff;
  border-left: 3px solid #1976d2;
  padding: 8px 10px;
  border-radius: 0 4px 4px 0;
  font-style: italic;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.channel {
  margin: 2px 0;
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.metadata-row {
  margin: 2px 0;
  font-size: 12px;
  color: #666;
  display: flex;
  align-items: center;
}

.views,
.upload-time {
  color: #666;
}

.separator {
  color: #666;
  margin: 0 2px;
}

.tag-container {
  margin: 0;
}

.added-tag {
  display: inline-block;
  background: #e3f2fd;
  color: #1976d2;
  padding: 1px 6px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
}

.date {
  margin: 0;
  font-size: 12px;
  color: #666;
}

.actions {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-left: 10px;
}

.transcript-btn,
.delete-btn {
  padding: 4px 8px;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 10px;
  font-weight: bold;
  transition: background-color 0.2s;
  min-width: 60px;
}

.transcript-btn {
  background-color: #1976d2;
  color: white;
}

.transcript-btn:hover {
  background-color: #1565c0;
}

/* Specific styles for different transcript states */
.transcript-fetching {
  background-color: #ff9800 !important;
  cursor: default;
}

.transcript-fetching:hover {
  background-color: #f57c00 !important;
}

.transcript-ready {
  background-color: #4caf50 !important;
}

.transcript-ready:hover {
  background-color: #45a049 !important;
}

.transcript-error {
  background-color: #f44336 !important;
}

.transcript-error:hover {
  background-color: #da190b !important;
}

.delete-btn {
  background-color: #f44336;
  color: white;
}

.delete-btn:hover {
  background-color: #da190b;
}

/* New video card structure styles */
.video-card {
  display: flex;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  margin-bottom: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: box-shadow 0.2s ease;
}

.video-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.left-column {
  position: relative;
  flex-shrink: 0;
}

.thumbnail-container {
  position: relative;
  width: 120px;
  height: 90px;
}

.thumbnail {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 0;
}

.thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.thumbnail img:hover {
  opacity: 0.8;
}

.duration {
  position: absolute;
  bottom: 4px;
  right: 4px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 2px 4px;
  border-radius: 2px;
  font-size: 11px;
  font-weight: 500;
  line-height: 1;
}

.thumbnail-actions {
  position: absolute;
  top: 4px;
  right: 4px;
  display: flex;
  gap: 4px;
}

.quick-action {
  background: rgba(0, 0, 0, 0.7);
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease, background-color 0.2s ease;
}

.video-card:hover .quick-action {
  opacity: 1;
}

.quick-action:hover {
  background: rgba(255, 0, 0, 0.8);
}

.quick-action svg {
  width: 12px;
  height: 12px;
  fill: white;
}

.tags {
  position: absolute;
  bottom: 4px;
  left: 4px;
  display: flex;
  gap: 4px;
}

.tag {
  padding: 2px 6px;
  border-radius: 2px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tag[data-category="new"] {
  background: #ff4444;
  color: white;
}

.tag[data-category="video"] {
  background: #4285f4;
  color: white;
}

.card-content {
  flex: 1;
  padding: 12px;
  display: flex;
  flex-direction: column;
}

.title-row {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 8px;
}

.channel-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #e0e0e0;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.channel-avatar.has-image img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.title-info {
  flex: 1;
  min-width: 0;
}

.video-title {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  line-height: 1.3;
  color: #0f0f0f;
  cursor: pointer;
  text-decoration: none;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.video-title:hover {
  color: #065fd4;
}

.video-meta {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #606060;
  line-height: 1.2;
}

.channel-name {
  color: #606060;
  font-weight: 400;
}

.meta-dot {
  width: 2px;
  height: 2px;
  background: #606060;
  border-radius: 50%;
  flex-shrink: 0;
}

.ai-response {
  margin: 8px 0;
  padding: 8px 12px 16px 24px;
  background: #f0f7ff;
  border-left: 3px solid #065fd4;
  border-radius: 0 4px 4px 0;
  font-size: 13px;
  line-height: 1.4;
  color: #333;
  font-style: italic;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.actions-row {
  margin-top: auto;
}

.actions-bar {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border: none;
  border-radius: 18px;
  background: #f2f2f2;
  color: #0f0f0f;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.action-btn:hover {
  background: #e5e5e5;
}

.action-btn svg {
  width: 16px;
  height: 16px;
  fill: currentColor;
}

.transcript-fetching {
  background: #ff9800 !important;
  color: white !important;
}

.transcript-ready {
  background: #4caf50 !important;
  color: white !important;
}

.transcript-error {
  background: #f44336 !important;
  color: white !important;
}

.status-indicator {
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-queued {
  background: #fff3cd;
  color: #856404;
}

.status-processing {
  background: #d1ecf1;
  color: #0c5460;
}

.status-complete {
  background: #d4edda;
  color: #155724;
}

.status-error {
  background: #f8d7da;
  color: #721c24;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #606060;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #0f0f0f;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
  line-height: 1.4;
}
