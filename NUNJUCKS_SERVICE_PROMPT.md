# Nunjucks Service Implementation Prompt

## Task Overview
Create a Nunjucks template service for the Firefox extension that replaces the simple string replacement currently used in `investigative-reporter-service.js` (line 15-18). This will serve as a test implementation before building a full template file loading system.

## Current Code Context

**File: `investigative-reporter-service.js`**
- Currently uses simple string replacement: `USER_PROMPT_TEMPLATE.replace("{{ transcript }}", transcript)`
- Line 15-18 needs to be replaced with Nunjucks rendering
- Already imports `USER_PROMPT_TEMPLATE` from `prompts/investigative-reporter-prompts.js`
- The template already uses Nunjucks syntax: `{{ transcript }}`

## Technical Requirements from Context7 Research

### Firefox Manifest V2 Compatibility
- Must work in Firefox extension background scripts
- Use global `nunjucks` object (assumes `lib/nunjucks.min.js` is loaded)
- No Node.js specific features
- No file system access needed for this implementation

### Exact Nunjucks API Usage (from Context7)
**Use this precise initialization syntax:**
```javascript
this.env = nunjucks.configure({ 
  autoescape: true,
  throwOnUndefined: false,
  trimBlocks: true,
  lstripBlocks: true
});
```

**Use this rendering method:**
```javascript
nunjucks.renderString(templateString, variables)
```

## Implementation Specifications

### Create: `services/nunjucks-service.js`

**Class Structure:**
```javascript
class NunjucksService {
  constructor() {
    // Initialize with exact Context7 configuration
    // Add custom filters
    // Create template cache
  }

  setupFilters() {
    // Add truncate filter for text limiting
    // Add any other useful filters
  }

  renderString(templateString, variables = {}) {
    // Use nunjucks.renderString() with error handling
    // Log errors with [Nunjucks Service] prefix
  }
}
```

**Required Custom Filters:**
- `truncate` filter: Limit text to specified length with ellipsis
- Format: `{{ text | truncate(100) }}`

**Error Handling:**
- Wrap all renders in try/catch blocks
- Use console.error with `[Nunjucks Service]` prefix
- Re-throw errors for upstream handling

### Integration Point

**Replace in `investigative-reporter-service.js`:**
```javascript
// BEFORE (lines 15-18):
const userPrompt = USER_PROMPT_TEMPLATE.replace("{{ transcript }}", transcript);

// AFTER:
const nunjucksService = new NunjucksService();
const userPrompt = nunjucksService.renderString(USER_PROMPT_TEMPLATE, { transcript });
```

### Export Pattern
Match the existing code style used in the project:
```javascript
// Export for both Node.js and browser environments
if (typeof module !== "undefined" && module.exports) {
  module.exports = { NunjucksService };
} else if (typeof window !== "undefined") {
  window.NunjucksService = NunjucksService;
}
```

## Testing Approach
- The existing `USER_PROMPT_TEMPLATE` already uses `{{ transcript }}` syntax
- Should work immediately with Nunjucks rendering
- Test by running the investigative reporter service

## Key Context7 Findings

### Browser Environment Limitations
- Nunjucks has NO native file loading in browsers
- `FileSystemLoader` doesn't exist in browser environments  
- `WebLoader` only works with HTTP URLs, not extension files
- Custom loaders are required for .njk file loading (future implementation)

### Firefox Extension Compatibility
- `browser.runtime.getURL()` + `fetch()` confirmed working for file loading
- No `web_accessible_resources` needed for background script access
- No CSP restrictions on extension file access
- Template files can be loaded from `templates/` directory in future

## Future Implementation Notes
This service will later be extended to:
1. Load .njk template files using `browser.runtime.getURL()` + `fetch()`
2. Implement template caching and batch loading
3. Support template inheritance and includes
4. Convert existing template literals to separate .njk files

## Critical Instructions
1. **Use the exact Context7 syntax provided above**
2. **If unsure about any Nunjucks API, research Context7 documentation first**
3. **Focus only on string template rendering for this implementation**
4. **Ensure Firefox Manifest V2 compatibility**
5. **Match existing code patterns and export styles**

This implementation serves as the foundation for a more sophisticated template system while providing immediate value by replacing string concatenation with proper template rendering.