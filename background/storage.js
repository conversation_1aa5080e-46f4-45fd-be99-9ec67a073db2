// Storage management functions for YouTube video bookmarker
// Handles all browser.storage.local operations with error handling

/**
 * Get videos from storage
 * @returns {Promise<Array>} Array of video objects
 */
async function getVideos() {
  try {
    const result = await browser.storage.local.get('videos');
    return result.videos || [];
  } catch (error) {
    console.error('[YT-Bookmarker] Error getting videos from storage:', error);
    return [];
  }
}

/**
 * Save video to storage
 * @param {Object} videoData - Video object to save  
 * @returns {Promise<boolean>} Success status
 */
async function saveVideo(videoData) {
  try {
    const currentVideos = await getVideos();
    
    // Check if video already exists
    if (currentVideos.some(video => video.id === videoData.id)) {
      console.log(`[YT-Bookmarker] Video ${videoData.id} already exists`);
      return false;
    }

    const updatedVideos = [videoData, ...currentVideos];
    await browser.storage.local.set({ videos: updatedVideos });
    console.log(`[YT-Bookmarker] Video ${videoData.id} saved successfully`);
    return true;
  } catch (error) {
    console.error('[YT-Bookmarker] Error saving video:', error);
    return false;
  }
}

/**
 * Update video in storage
 * @param {string} videoId - Video ID to update
 * @param {Object} updates - Updates to apply
 * @returns {Promise<boolean>} Success status
 */
async function updateVideo(videoId, updates) {
  try {
    const currentVideos = await getVideos();
    const videoIndex = currentVideos.findIndex(v => v.id === videoId);
    
    if (videoIndex === -1) {
      console.warn(`[YT-Bookmarker] Video ${videoId} not found for update`);
      return false;
    }

    currentVideos[videoIndex] = { ...currentVideos[videoIndex], ...updates };
    await browser.storage.local.set({ videos: currentVideos });
    console.log(`[YT-Bookmarker] Video ${videoId} updated successfully`);
    return true;
  } catch (error) {
    console.error(`[YT-Bookmarker] Error updating video ${videoId}:`, error);
    return false;
  }
}

/**
 * Remove video from storage
 * @param {string} videoId - Video ID to remove
 * @returns {Promise<boolean>} Success status
 */
async function removeVideo(videoId) {
  try {
    const currentVideos = await getVideos();
    const updatedVideos = currentVideos.filter(video => video.id !== videoId);
    
    if (currentVideos.length === updatedVideos.length) {
      console.warn(`[YT-Bookmarker] Video ${videoId} not found for removal`);
      return false;
    }

    await browser.storage.local.set({ videos: updatedVideos });
    console.log(`[YT-Bookmarker] Video ${videoId} removed successfully`);
    return true;
  } catch (error) {
    console.error(`[YT-Bookmarker] Error removing video ${videoId}:`, error);
    return false;
  }
}

/**
 * Update video status (transcriptStatus, etc.)
 * @param {string} videoId - Video ID
 * @param {string} status - New status
 * @param {Object} additionalData - Additional data to update
 * @returns {Promise<boolean>} Success status
 */
async function updateVideoStatus(videoId, status, additionalData = {}) {
  const updates = { transcriptStatus: status, ...additionalData };
  return await updateVideo(videoId, updates);
}

/**
 * Get API key from storage
 * @param {string} keyName - API key name (apify_api_key, openai_api_key, gemini_api_key)
 * @returns {Promise<string|null>} API key or null
 */
async function getApiKey(keyName) {
  try {
    const result = await browser.storage.local.get(keyName);
    return result[keyName] || null;
  } catch (error) {
    console.error(`[YT-Bookmarker] Error getting API key ${keyName}:`, error);
    return null;
  }
}

/**
 * Set API key in storage
 * @param {string} keyName - API key name
 * @param {string} keyValue - API key value
 * @returns {Promise<boolean>} Success status
 */
async function setApiKey(keyName, keyValue) {
  try {
    await browser.storage.local.set({ [keyName]: keyValue });
    console.log(`[YT-Bookmarker] API key ${keyName} saved successfully`);
    return true;
  } catch (error) {
    console.error(`[YT-Bookmarker] Error saving API key ${keyName}:`, error);
    return false;
  }
}

/**
 * Check if all required API keys are configured
 * @returns {Promise<Object>} Status of each API key
 */
async function checkApiKeysStatus() {
  try {
    const result = await browser.storage.local.get([
      'apify_api_key',
      'openai_api_key', 
      'gemini_api_key',
      'api_keys_configured',
      'api_key_setup_skipped'
    ]);

    return {
      hasApifyKey: !!result.apify_api_key,
      hasOpenAIKey: !!result.openai_api_key,
      hasGeminiKey: !!result.gemini_api_key,
      isConfigured: !!result.api_keys_configured,
      isSkipped: !!result.api_key_setup_skipped,
      hasAllKeys: !!(result.apify_api_key && result.openai_api_key && result.gemini_api_key)
    };
  } catch (error) {
    console.error('[YT-Bookmarker] Error checking API keys status:', error);
    return {
      hasApifyKey: false,
      hasOpenAIKey: false, 
      hasGeminiKey: false,
      isConfigured: false,
      isSkipped: false,
      hasAllKeys: false
    };
  }
}

/**
 * Clear all storage data (for testing/reset)
 * @returns {Promise<boolean>} Success status
 */
async function clearAllData() {
  try {
    await browser.storage.local.clear();
    console.log('[YT-Bookmarker] All storage data cleared');
    return true;
  } catch (error) {
    console.error('[YT-Bookmarker] Error clearing storage data:', error);
    return false;
  }
}

// Export for both CommonJS (tests) and global scope (extension)
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    getVideos,
    saveVideo,
    updateVideo,
    removeVideo,
    updateVideoStatus,
    getApiKey,
    setApiKey,
    checkApiKeysStatus,
    clearAllData
  };
} else {
  // For browser extension context
  window.StorageManager = {
    getVideos,
    saveVideo,
    updateVideo,
    removeVideo,
    updateVideoStatus,
    getApiKey,
    setApiKey,
    checkApiKeysStatus,
    clearAllData
  };
}