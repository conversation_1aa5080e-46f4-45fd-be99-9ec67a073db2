// Apify YouTube Transcript Service
// Handles transcript retrieval using Apify actor API

// Apify API Configuration
const APIFY_CONFIG = {
  ACTOR_ID: "faVsWy9VTSNVIhWpR",
  BASE_URL:
    "https://api.apify.com/v2/acts/pintostudio~youtube-transcript-scraper/runs",
  POLL_INTERVAL: 2000, // 2 seconds
  MAX_POLL_TIME: 60000, // 60 seconds max wait
  MAX_RETRIES: 3,
};

// Get Apify API token from storage
async function getApifyApiToken() {
  try {
    // Use storage manager if available, otherwise direct browser API
    if (typeof StorageManager !== 'undefined') {
      return await StorageManager.getApiKey('apify_api_key');
    } else {
      const result = await browser.storage.local.get('apify_api_key');
      if (!result.apify_api_key) {
        throw new Error('Apify API key not configured');
      }
      return result.apify_api_key;
    }
  } catch (error) {
    debugLog('Error getting Apify API token:', error.message);
    throw error;
  }
}

// Debug logging function
function debugLog(...args) {
  const message = `[Apify-Transcript] ${args.join(" ")}`;
  console.log(message);
}

// Utility function to sleep/wait
function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

// Start an Apify actor run
async function startApifyRun(videoUrl) {
  debugLog("Starting Apify run for video:", videoUrl);

  const apiToken = await getApifyApiToken();
  const url = `${APIFY_CONFIG.BASE_URL}?token=${apiToken}`;
  const payload = {
    videoUrl: videoUrl,
  };

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    debugLog("Apify run started:", data.id);
    return data.id;
  } catch (error) {
    debugLog("Error starting Apify run:", error.message);
    throw new Error(`Failed to start transcript extraction: ${error.message}`);
  }
}

// Poll run status until completion
async function pollRunStatus(runId) {
  debugLog("Polling run status for:", runId);

  const apiToken = await getApifyApiToken();
  const url = `${APIFY_CONFIG.BASE_URL}/${runId}?token=${apiToken}`;
  const startTime = Date.now();

  while (Date.now() - startTime < APIFY_CONFIG.MAX_POLL_TIME) {
    try {
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      debugLog("Run status:", data.status);

      if (data.status === "SUCCEEDED") {
        debugLog(
          "Run completed successfully, dataset ID:",
          data.defaultDatasetId,
        );
        return data.defaultDatasetId;
      } else if (data.status === "FAILED") {
        throw new Error("Apify actor run failed");
      } else if (data.status === "ABORTED") {
        throw new Error("Apify actor run was aborted");
      }

      // Wait before next poll
      await sleep(APIFY_CONFIG.POLL_INTERVAL);
    } catch (error) {
      debugLog("Error polling run status:", error.message);
      throw new Error(`Failed to check extraction status: ${error.message}`);
    }
  }

  throw new Error("Transcript extraction timed out");
}

// Fetch transcript data from dataset
async function fetchTranscriptData(datasetId) {
  debugLog("Fetching transcript data from dataset:", datasetId);

  const apiToken = await getApifyApiToken();
  const url = `https://api.apify.com/v2/datasets/${datasetId}/items?token=${apiToken}`;

  try {
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    debugLog("Dataset response received, items count:", data.length);
    return data;
  } catch (error) {
    debugLog("Error fetching transcript data:", error.message);
    throw new Error(`Failed to fetch transcript data: ${error.message}`);
  }
}

// Parse Apify response and extract transcript text (simplified for sync endpoint)
function parseTranscriptResponse(datasetItems) {
  debugLog("Parsing sync transcript response");

  if (!datasetItems || datasetItems.length === 0) {
    debugLog("ERROR: No dataset items found");
    throw new Error("No transcript data found in response");
  }

  debugLog("Number of dataset items:", datasetItems.length);
  const firstItem = datasetItems[0];

  if (!firstItem.data || !Array.isArray(firstItem.data)) {
    debugLog("ERROR: Invalid data format - firstItem.data:", firstItem.data);
    throw new Error("Invalid transcript data format");
  }

  debugLog("Number of transcript segments:", firstItem.data.length);

  // Extract text from all segments and combine
  const transcriptText = firstItem.data
    .filter((segment) => segment.text && segment.text.trim())
    .map((segment) => segment.text)
    .join(" ")
    .trim();

  debugLog("Final transcript text length:", transcriptText.length);

  if (!transcriptText) {
    debugLog("ERROR: No transcript text extracted");
    throw new Error("No transcript text found");
  }

  debugLog("Transcript extracted successfully, length:", transcriptText.length);
  return transcriptText;
}

// Main function to get transcript using Apify (Simplified sync version)
async function getApifyTranscript(videoId) {
  // Construct full YouTube URL
  const videoUrl = `https://www.youtube.com/watch?v=${videoId}`;

  try {
    // Use the simplified sync endpoint
    const apiToken = await getApifyApiToken();
    const url = `${APIFY_CONFIG.BASE_URL.replace(
      "/runs",
      "/run-sync-get-dataset-items",
    )}?token=${apiToken}`;

    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        videoUrl: videoUrl,
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const datasetItems = await response.json();

    // Parse and return transcript
    const transcript = parseTranscriptResponse(datasetItems);

    return transcript;
  } catch (error) {
    debugLog("Sync API failed:", error.message);
    throw error;
  }
}

// Export functions for both CommonJS (tests) and global scope (extension)
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    getApifyTranscript,
    startApifyRun,
    pollRunStatus,
    fetchTranscriptData,
    parseTranscriptResponse,
    getApifyApiToken,
    debugLog,
    APIFY_CONFIG
  };
} else {
  // For browser extension context
  if (typeof window !== "undefined") {
    window.getApifyTranscript = getApifyTranscript;
    window.ApifyTranscriptService = {
      getApifyTranscript,
      startApifyRun,
      pollRunStatus,
      fetchTranscriptData,
      parseTranscriptResponse,
      getApifyApiToken,
      debugLog,
      APIFY_CONFIG
    };
  } else {
    // For Firefox extension background context
    this.getApifyTranscript = getApifyTranscript;
    this.ApifyTranscriptService = {
      getApifyTranscript,
      startApifyRun,
      pollRunStatus,
      fetchTranscriptData,
      parseTranscriptResponse,
      getApifyApiToken,
      debugLog,
      APIFY_CONFIG
    };
  }
}
