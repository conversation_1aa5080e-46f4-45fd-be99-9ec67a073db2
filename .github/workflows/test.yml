name: Test Firefox Extension

on:
  push:
    branches: [ master, main, develop ]
  pull_request:
    branches: [ master, main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18, 20]
        
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run unit tests
      run: npm test
      
    - name: Run tests with coverage
      run: npm run test:coverage
      
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false
        
  lint-and-build:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Install web-ext globally
      run: npm install -g web-ext
      
    - name: Lint extension
      run: web-ext lint
      
    - name: Build extension
      run: web-ext build
      
    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: firefox-extension
        path: web-ext-artifacts/
        retention-days: 7
        
  integration-test:
    runs-on: ubuntu-latest
    needs: [test, lint-and-build]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Install Firefox
      uses: browser-actions/setup-firefox@v1
      with:
        firefox-version: 'latest'
        
    - name: Install web-ext
      run: npm install -g web-ext
      
    - name: Run integration tests
      run: |
        # Create test profile
        firefox --headless --createprofile test-profile
        
        # Run extension in test mode (timeout after 30 seconds)
        timeout 30s web-ext run --firefox-profile=test-profile --start-url about:debugging || true
        
        echo "Integration test completed"
        
    - name: Check extension health
      run: |
        # Basic health check - ensure manifest is valid
        node -e "
          const manifest = require('./manifest.json');
          console.log('Extension name:', manifest.name);
          console.log('Version:', manifest.version);
          console.log('Manifest version:', manifest.manifest_version);
          
          if (manifest.manifest_version !== 2) {
            throw new Error('Expected Manifest V2');
          }
          
          if (!manifest.background || !manifest.background.scripts) {
            throw new Error('Background scripts not defined');
          }
          
          console.log('Manifest validation passed');
        "
        
  security-scan:
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run security scan
      run: |
        # Check for common security issues in extension code
        echo "Running security scan..."
        
        # Check for eval usage (security risk)
        if grep -r "eval(" --include="*.js" .; then
          echo "WARNING: eval() usage detected"
          exit 1
        fi
        
        # Check for innerHTML usage (XSS risk)
        if grep -r "innerHTML" --include="*.js" .; then
          echo "WARNING: innerHTML usage detected - prefer textContent"
        fi
        
        # Check for external script loading
        if grep -r "document.createElement.*script" --include="*.js" .; then
          echo "WARNING: Dynamic script creation detected"
        fi
        
        echo "Security scan completed"