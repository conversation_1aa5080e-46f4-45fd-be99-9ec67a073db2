// Console test script for YouTube Summarizer Extension
// Run this in the browser console on any YouTube page to test functionality

console.log("=== YouTube Summarizer Extension Test ===");

// Test 1: Check if extension is loaded
async function testExtensionLoaded() {
    console.log("\n--- Test 1: Extension Loading ---");
    try {
        // Try to access extension storage
        const result = await browser.storage.local.get('videos');
        console.log("✅ Extension is loaded and accessible");
        console.log("Current videos in storage:", result.videos?.length || 0);
        return true;
    } catch (error) {
        console.log("❌ Extension not loaded or not accessible:", error.message);
        return false;
    }
}

// Test 2: Check content script functionality
async function testContentScript() {
    console.log("\n--- Test 2: Content Script Communication ---");
    try {
        // Test basic content script communication
        const response = await browser.runtime.sendMessage({ action: "ping" });
        console.log("✅ Content script communication working");
        return true;
    } catch (error) {
        console.log("❌ Content script communication failed:", error.message);
        return false;
    }
}

// Test 3: Analyze video elements on current page
function testVideoElementAnalysis() {
    console.log("\n--- Test 3: Video Element Analysis ---");
    
    const videos = document.querySelectorAll('video');
    console.log(`Found ${videos.length} video elements`);
    
    const videoContainers = document.querySelectorAll('.ytd-rich-item-renderer, .ytd-video-renderer, .ytd-compact-video-renderer');
    console.log(`Found ${videoContainers.length} video container elements`);
    
    const dataVideoElements = document.querySelectorAll('[data-video-id]');
    console.log(`Found ${dataVideoElements.length} elements with data-video-id`);
    
    // Show first few video IDs found
    const videoIds = [];
    dataVideoElements.forEach((el, index) => {
        if (index < 5) {
            const videoId = el.getAttribute('data-video-id');
            if (videoId) {
                videoIds.push(videoId);
                console.log(`Video ID ${index + 1}: ${videoId}`);
            }
        }
    });
    
    return videoIds.length > 0;
}

// Test 4: Test video ID extraction
function testVideoIdExtraction() {
    console.log("\n--- Test 4: Video ID Extraction ---");
    
    // Test URL extraction
    const currentUrl = window.location.href;
    const urlRegex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=|shorts\/)|youtu\.be\/)([^"&?\/\s]{11})/;
    const urlMatch = currentUrl.match(urlRegex);
    
    if (urlMatch) {
        console.log("✅ Video ID from current URL:", urlMatch[1]);
        return urlMatch[1];
    }
    
    // Test DOM extraction
    const dataVideoElement = document.querySelector('[data-video-id]');
    if (dataVideoElement) {
        const videoId = dataVideoElement.getAttribute('data-video-id');
        console.log("✅ Video ID from DOM:", videoId);
        return videoId;
    }
    
    console.log("❌ No video ID found");
    return null;
}

// Test 5: Simulate context menu detection
function testContextMenuDetection() {
    console.log("\n--- Test 5: Context Menu Detection Simulation ---");
    
    // Find video elements and simulate right-click detection
    const videos = document.querySelectorAll('video');
    const videoContainers = document.querySelectorAll('.ytd-rich-item-renderer, .ytd-video-renderer');
    
    console.log(`Testing ${videos.length} video elements and ${videoContainers.length} containers`);
    
    let detectedVideoIds = [];
    
    // Test video elements
    videos.forEach((video, index) => {
        console.log(`\nTesting video element ${index + 1}:`);
        
        // Check for nearby video ID
        let container = video.closest('.ytd-rich-item-renderer, .ytd-video-renderer, .ytd-compact-video-renderer');
        if (!container) {
            container = video.parentElement;
        }
        
        if (container) {
            const dataVideoId = container.getAttribute('data-video-id') || 
                               container.querySelector('[data-video-id]')?.getAttribute('data-video-id');
            
            if (dataVideoId) {
                console.log(`  ✅ Found video ID: ${dataVideoId}`);
                detectedVideoIds.push(dataVideoId);
            } else {
                console.log(`  ❌ No video ID found for this video element`);
            }
        }
    });
    
    return detectedVideoIds;
}

// Test 6: Test extension context menu functionality (if available)
async function testExtensionContextMenu() {
    console.log("\n--- Test 6: Extension Context Menu Test ---");
    
    try {
        // This would only work if we're in the extension context
        if (typeof browser !== 'undefined' && browser.contextMenus) {
            console.log("✅ Context menu API is available");
            return true;
        } else {
            console.log("ℹ️ Context menu API not available in this context (normal for content script)");
            return false;
        }
    } catch (error) {
        console.log("❌ Context menu test failed:", error.message);
        return false;
    }
}

// Run all tests
async function runAllTests() {
    console.log("Starting comprehensive extension tests...\n");
    
    const results = {
        extensionLoaded: await testExtensionLoaded(),
        contentScript: await testContentScript(),
        videoAnalysis: testVideoElementAnalysis(),
        videoIdExtraction: testVideoIdExtraction(),
        contextMenuDetection: testContextMenuDetection(),
        extensionContextMenu: await testExtensionContextMenu()
    };
    
    console.log("\n=== Test Results Summary ===");
    Object.entries(results).forEach(([test, result]) => {
        const status = result ? "✅ PASS" : "❌ FAIL";
        console.log(`${test}: ${status}`);
    });
    
    console.log("\n=== Recommendations ===");
    if (!results.extensionLoaded) {
        console.log("- Make sure the extension is loaded in Firefox");
        console.log("- Check that the extension has proper permissions");
    }
    
    if (!results.videoAnalysis) {
        console.log("- This page might not have video thumbnails");
        console.log("- Try running on YouTube homepage or search results");
    }
    
    if (!results.videoIdExtraction) {
        console.log("- No video IDs detected on this page");
        console.log("- Try on a page with video thumbnails or a watch page");
    }
    
    console.log("\n=== Next Steps ===");
    console.log("1. Right-click on video thumbnails and check for context menu");
    console.log("2. Check browser console for extension debug logs");
    console.log("3. Try the context menu on different types of video elements");
    
    return results;
}

// Auto-run tests
runAllTests().catch(console.error);
