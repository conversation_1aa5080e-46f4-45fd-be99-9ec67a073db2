const fs = require('fs');

// Load environment variables from .env file
const envContent = fs.readFileSync('.env', 'utf8');
const envVars = {};
envContent.split('\n').forEach(line => {
  const [key, value] = line.split('=');
  if (key && value) {
    envVars[key.trim()] = value.trim();
  }
});
process.env.DEEPSEEK_API_KEY = envVars.DEEPSEEK_API_KEY;

async function chatCompletion(prompt, options = {}) {
  const {
    model = 'deepseek-chat',
    maxTokens = 150,
    temperature = 0.7,
    systemMessage = 'You are a helpful assistant.'
  } = options;

  const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${process.env.DEEPSEEK_API_KEY}`
    },
    body: JSON.stringify({
      model: model,
      messages: [
        {
          role: 'system',
          content: systemMessage
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      max_tokens: maxTokens,
      temperature: temperature
    })
  });

  if (!response.ok) {
    throw new Error(`API request failed: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();
  return data;
}

// Test the function
async function test() {
  try {
    console.log('Testing DeepSeek Chat Completions API...');
    
    const prompt = 'Hello!';
    const response = await chatCompletion(prompt);
    
    console.log('Prompt:', prompt);
    console.log('Full Response Object:');
    console.log(JSON.stringify(response, null, 2));
    
    console.log('\nExtracted Content:', response.choices[0].message.content);
    console.log('Usage:', response.usage);
    console.log('Model:', response.model);
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

test();