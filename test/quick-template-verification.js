// Quick verification script to test template service locally
// This can be run in Node.js to verify the template logic works

// Simulate the template service class locally
class SimpleTemplateService {
  renderString(templateString, variables = {}) {
    try {
      let result = templateString;
      
      // Replace all {{ variable }} patterns with actual values
      for (const [key, value] of Object.entries(variables)) {
        // Create regex to match {{ key }} with optional whitespace
        const regex = new RegExp(`\\{\\{\\s*${this.escapeRegex(key)}\\s*\\}\\}`, 'g');
        
        // Replace with the actual value (convert to string)
        const replacement = value != null ? String(value) : '';
        result = result.replace(regex, replacement);
      }
      
      // Check for any remaining unreplaced variables and warn
      const remainingVars = result.match(/\{\{\s*\w+\s*\}\}/g);
      if (remainingVars) {
        console.warn('Unreplaced template variables:', remainingVars);
      }
      
      return result;
    } catch (error) {
      console.error('Template render error:', error);
      throw new Error(`Template rendering failed: ${error.message}`);
    }
  }

  escapeRegex(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }
}

// Test the template patterns from the actual prompts
function testTemplatePatterns() {
  console.log('=== Testing Template Patterns ===\n');
  
  const templateService = new SimpleTemplateService();
  
  // Test 1: Basic transcript pattern
  console.log('Test 1: Transcript Cleaner Pattern');
  const transcriptTemplate = `Please clean up this raw YouTube transcript.

Raw Transcript:
---
{{ transcript }}
---

Return the cleaned transcript:`;
  
  const transcriptResult = templateService.renderString(transcriptTemplate, {
    transcript: "Um, so like, this is a test transcript, you know? It has, uh, filler words and stuff."
  });
  
  console.log('✅ Transcript template rendered successfully');
  console.log('Result preview:', transcriptResult.substring(0, 150) + '...\n');
  
  // Test 2: Title response pattern
  console.log('Test 2: Title Response Pattern');
  const titleTemplate = `VIDEO TITLE: {{title}}

TRANSCRIPT: {{transcript}}

Generate a Title Response:`;
  
  const titleResult = templateService.renderString(titleTemplate, {
    title: "How to Fix CSP Errors in Firefox Extensions",
    transcript: "In this video we learn about Content Security Policy and how to avoid eval() functions..."
  });
  
  console.log('✅ Title response template rendered successfully');
  console.log('Result preview:', titleResult.substring(0, 150) + '...\n');
  
  // Test 3: Article generation pattern
  console.log('Test 3: Investigative Reporter Pattern');
  const articleTemplate = `Transform this transcript:

{{ transcript }}

Write the technical article:`;
  
  const articleResult = templateService.renderString(articleTemplate, {
    transcript: "Today we're discussing advanced browser security features including CSP..."
  });
  
  console.log('✅ Article template rendered successfully');
  console.log('Result preview:', articleResult.substring(0, 150) + '...\n');
  
  // Test 4: Edge cases
  console.log('Test 4: Edge Cases');
  
  // Whitespace variations
  const whitespaceTest = templateService.renderString('{{ name}} and {{title }} and {{ content}}', {
    name: 'Test',
    title: 'Video',
    content: 'Content'
  });
  console.log('✅ Whitespace variations:', whitespaceTest);
  
  // Missing variables
  const missingTest = templateService.renderString('Hello {{ name }}, {{ missing }} not provided', {
    name: 'User'
  });
  console.log('✅ Missing variables handled:', missingTest);
  
  // Special characters
  const specialTest = templateService.renderString('Content: {{ content }}', {
    content: 'Has [brackets] and {braces} and (parentheses) and $pecial chars!'
  });
  console.log('✅ Special characters:', specialTest);
  
  // Very long content (like real transcripts)
  const longContent = 'A'.repeat(5000) + ' transcript content ' + 'B'.repeat(5000);
  const longTest = templateService.renderString('Transcript: {{ transcript }}', {
    transcript: longContent
  });
  console.log('✅ Long content handled, length:', longTest.length);
  
  console.log('\n🎉 All template pattern tests passed!');
  console.log('The CSP-safe template service should work correctly.');
}

// Run the tests
testTemplatePatterns();