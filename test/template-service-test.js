// Template Service Test - Run in browser console to verify CSP-safe template rendering
// Load this after the extension is loaded to test the new SimpleTemplateService

console.log("=== Template Service Test ===");

// Test the template service functionality
function testTemplateService() {
    console.log("\n--- Testing SimpleTemplateService ---");
    
    try {
        // Check if the service is available
        if (typeof NunjucksService === 'undefined') {
            console.log("❌ NunjucksService (SimpleTemplateService) is not available");
            return false;
        }
        
        // Create a template service instance
        const templateService = new NunjucksService();
        console.log("✅ Template service created successfully");
        
        // Test basic variable substitution
        const template1 = "Hello {{ name }}, welcome to {{ place }}!";
        const result1 = templateService.renderString(template1, { 
            name: "YouTube", 
            place: "the extension" 
        });
        console.log("✅ Basic substitution test:");
        console.log(`   Template: ${template1}`);
        console.log(`   Result: ${result1}`);
        
        // Test with whitespace variations
        const template2 = "{{title}} has {{count}} items";
        const result2 = templateService.renderString(template2, { 
            title: "Video List", 
            count: 42 
        });
        console.log("✅ Whitespace variation test:");
        console.log(`   Template: ${template2}`);
        console.log(`   Result: ${result2}`);
        
        // Test transcript-like template (similar to actual usage)
        const transcriptTemplate = `Clean up this transcript: {{ transcript }}`;
        const sampleTranscript = "Um, so like, this is a test transcript, you know?";
        const result3 = templateService.renderString(transcriptTemplate, { 
            transcript: sampleTranscript 
        });
        console.log("✅ Transcript template test:");
        console.log(`   Result: ${result3}`);
        
        // Test title response template (multi-variable)
        const titleTemplate = `VIDEO TITLE: {{title}}\n\nTRANSCRIPT: {{transcript}}\n\nGenerate response...`;
        const result4 = templateService.renderString(titleTemplate, { 
            title: "How to Fix CSP Errors",
            transcript: "In this video we'll learn about Content Security Policy..."
        });
        console.log("✅ Multi-variable template test:");
        console.log(`   Result preview: ${result4.substring(0, 100)}...`);
        
        // Test missing variable handling
        const templateWithMissing = "Hello {{ name }}, {{ missing_var }} is not provided";
        const result5 = templateService.renderString(templateWithMissing, { 
            name: "Test" 
        });
        console.log("✅ Missing variable test:");
        console.log(`   Result: ${result5}`);
        
        // Test special characters in variables
        const templateSpecial = "Content: {{ content }}";
        const result6 = templateService.renderString(templateSpecial, { 
            content: "This has [brackets] and {braces} and (parentheses)" 
        });
        console.log("✅ Special characters test:");
        console.log(`   Result: ${result6}`);
        
        console.log("\n🎉 All template service tests passed!");
        return true;
        
    } catch (error) {
        console.log("❌ Template service test failed:", error.message);
        console.error(error);
        return false;
    }
}

// Test the actual prompt templates used by the extension
function testPromptTemplates() {
    console.log("\n--- Testing Actual Prompt Templates ---");
    
    try {
        if (typeof window.TRANSCRIPT_CLEANER_PROMPTS === 'undefined') {
            console.log("❌ TRANSCRIPT_CLEANER_PROMPTS not available");
            return false;
        }
        
        const templateService = new NunjucksService();
        
        // Test transcript cleaner template
        const { USER_PROMPT_TEMPLATE: cleanerTemplate } = window.TRANSCRIPT_CLEANER_PROMPTS;
        const cleanerResult = templateService.renderString(cleanerTemplate, { 
            transcript: "Sample transcript for testing" 
        });
        console.log("✅ Transcript cleaner template renders successfully");
        console.log(`   Template length: ${cleanerTemplate.length}`);
        console.log(`   Result length: ${cleanerResult.length}`);
        
        // Test title response template if available
        if (typeof window.TITLE_RESPONSE_PROMPTS !== 'undefined') {
            const { USER_PROMPT_TEMPLATE: titleTemplate } = window.TITLE_RESPONSE_PROMPTS;
            const titleResult = templateService.renderString(titleTemplate, { 
                title: "Test Video Title",
                transcript: "Test transcript content"
            });
            console.log("✅ Title response template renders successfully");
            console.log(`   Template length: ${titleTemplate.length}`);
            console.log(`   Result length: ${titleResult.length}`);
        }
        
        // Test investigative reporter template if available
        if (typeof window.INVESTIGATIVE_REPORTER_PROMPTS !== 'undefined') {
            const { USER_PROMPT_TEMPLATE: reporterTemplate } = window.INVESTIGATIVE_REPORTER_PROMPTS;
            const reporterResult = templateService.renderString(reporterTemplate, { 
                transcript: "Test transcript for article generation"
            });
            console.log("✅ Investigative reporter template renders successfully");
            console.log(`   Template length: ${reporterTemplate.length}`);
            console.log(`   Result length: ${reporterResult.length}`);
        }
        
        console.log("\n🎉 All prompt template tests passed!");
        return true;
        
    } catch (error) {
        console.log("❌ Prompt template test failed:", error.message);
        console.error(error);
        return false;
    }
}

// Test for CSP errors
function testCSPCompliance() {
    console.log("\n--- Testing CSP Compliance ---");
    
    try {
        // The mere fact that we can create and use the template service
        // without CSP errors is a good sign
        const templateService = new NunjucksService();
        
        // Try to render a template - this should not trigger CSP errors
        const result = templateService.renderString("Test {{ var }}", { var: "passed" });
        
        if (result === "Test passed") {
            console.log("✅ No CSP errors detected during template rendering");
            console.log("✅ Template service is CSP-compliant");
            return true;
        } else {
            console.log("❌ Template rendering produced unexpected result:", result);
            return false;
        }
        
    } catch (error) {
        console.log("❌ CSP compliance test failed:", error.message);
        
        // Check if the error mentions CSP or eval
        if (error.message.includes('CSP') || error.message.includes('eval')) {
            console.log("🚨 This appears to be a CSP-related error!");
        }
        
        return false;
    }
}

// Run all template tests
async function runTemplateTests() {
    console.log("Starting template service tests...\n");
    
    const results = {
        templateService: testTemplateService(),
        promptTemplates: testPromptTemplates(),
        cspCompliance: testCSPCompliance()
    };
    
    console.log("\n=== Template Test Results ===");
    Object.entries(results).forEach(([test, result]) => {
        const status = result ? "✅ PASS" : "❌ FAIL";
        console.log(`${test}: ${status}`);
    });
    
    const allPassed = Object.values(results).every(result => result);
    
    if (allPassed) {
        console.log("\n🎉 All template tests passed! CSP issue should be resolved.");
        console.log("The 3-stage pipeline should now work without CSP errors.");
    } else {
        console.log("\n❌ Some template tests failed. CSP issue may persist.");
        console.log("Check the specific failures above for debugging information.");
    }
    
    return results;
}

// Auto-run tests
runTemplateTests().catch(console.error);