const OpenAIService = require("../services/openai-completion-service.js");
const { SYSTEM_PROMPT, USER_PROMPT_TEMPLATE } = require("../prompts/investigative-reporter-prompts.js");

async function transformTranscriptToArticle(transcript) {
  if (
    !transcript ||
    typeof transcript !== "string" ||
    transcript.trim() === ""
  ) {
    throw new Error("Transcript must be a non-empty string");
  }

  const openai = new OpenAIService();

  const nunjucksService = new NunjucksService();
  const userPrompt = nunjucksService.renderString(USER_PROMPT_TEMPLATE, { transcript });

  const response = await openai.withSystem(SYSTEM_PROMPT, userPrompt, {
    model: "gpt-4.1",
    temperature: 0.7,
    max_tokens: 2000,
  });

  return response.getContent();
}

// Export for Node.js
if (typeof module !== "undefined" && module.exports) {
  module.exports = { transformTranscriptToArticle };
}
