const { transformTranscriptToArticle } = require('./investigative-reporter-service.js');

async function testReporter() {
  console.log('🧪 Testing Investigative Reporter Service\n');

  // Sample transcript content (simulating a tech YouTube video)
  const sampleTranscript = `
[00:00] Hey everyone, welcome back to the channel! Today I'm super excited to show you this breakthrough in quantum computing that nobody's talking about.

[00:15] So I've been working with the team at QuantumTech Labs, and we've achieved something incredible. We've managed to get our new quantum processor to maintain coherence for 10 milliseconds at room temperature.

[00:30] Now, I know what you're thinking - that doesn't sound like much, but current state-of-the-art systems only achieve 1-2 milliseconds and require cooling to near absolute zero. This is a 500% improvement!

[00:45] The secret sauce is our new error correction algorithm combined with topological qubits. We're using a hybrid approach with 50 logical qubits that can perform calculations equivalent to 1000 traditional qubits.

[01:00] In our benchmarks, we solved the traveling salesman problem for 20 cities in just 0.3 seconds. Classical computers take about 45 minutes for the same problem.

[01:15] Now, I have to be honest - we haven't published this in a peer-reviewed journal yet, but the results are so promising that I wanted to share them with you first.

[01:30] The implications are massive. We could see practical quantum advantages in logistics, drug discovery, and cryptography within the next 2 years instead of the projected 10-15 years.

[01:45] I'll link to our preprint in the description, and make sure to subscribe for more quantum computing content!
`;

  try {
    console.log('📝 Sample transcript length:', sampleTranscript.length, 'characters');
    console.log('\n🤖 Generating Slashdot article...\n');
    
    const article = await transformTranscriptToArticle(sampleTranscript);
    
    console.log('📰 Generated Article:');
    console.log('=' .repeat(80));
    console.log(article);
    console.log('=' .repeat(80));
    console.log('\n✅ Article generation completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testReporter();