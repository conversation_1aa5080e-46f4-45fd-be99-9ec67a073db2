# Testing Documentation

This directory contains automated tests for the YouTube Video Bookmarker Firefox extension.

## Test Structure

```
test/
├── unit/                     # Unit tests for individual components
│   ├── video-processor.test.js    # Video URL processing and ID extraction
│   ├── storage.test.js            # Browser storage operations
│   ├── investigative-reporter.test.js  # AI article generation
│   └── context-menu.test.js       # Context menu functionality
├── integration/              # Integration tests
│   └── extension.test.js          # Full extension integration tests
├── setup.js                 # Jest test setup and mocking
└── README.md                 # This file
```

## Running Tests

### Prerequisites
```bash
npm install
```

### Available Test Commands

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage report
npm run test:coverage

# Lint extension code
npm run lint

# Build extension
npm run build
```

## Test Categories

### Unit Tests
- **video-processor.test.js**: Tests video ID extraction from various YouTube URL formats
- **storage.test.js**: Tests browser storage operations (save, retrieve, update videos)
- **investigative-reporter.test.js**: Tests AI-powered article generation with mocked APIs
- **context-menu.test.js**: Tests context menu creation and click handling

### Integration Tests
- **extension.test.js**: Tests complete extension loading, manifest validation, and component integration

## Mocking Strategy

The test suite uses comprehensive mocking for browser APIs:

### Browser API Mocks
- `browser.storage.local` - Firefox extension storage
- `browser.contextMenus` - Context menu creation and events
- `browser.tabs` - Tab management operations
- `browser.runtime` - Extension runtime operations

### Service Mocks
- `BrowserOpenAIService` - AI API service with mock responses
- `NunjucksService` - Template rendering service
- `fetch` - HTTP requests for external APIs

## Test Setup

The `setup.js` file configures:
- Jest environment with jsdom
- WebExtension API mocks
- Global variables and services
- Console error filtering
- Mock reset between tests

## Coverage Requirements

Current coverage thresholds:
- Branches: 70%
- Functions: 70%
- Lines: 70%
- Statements: 70%

## CI/CD Integration

Tests run automatically on:
- Push to main branches
- Pull requests
- Multiple Node.js versions (18, 20)

GitHub Actions workflow includes:
- Unit test execution
- Coverage reporting
- Extension linting with web-ext
- Build verification
- Security scanning

## Manual Testing

For manual testing, use the existing tools:
- `console-test.js` - Browser console test suite
- `debug/extension-status.html` - Extension health checker
- `web-ext run` - Live development with auto-reload

## Adding New Tests

When adding new functionality:

1. **Create unit tests** for individual functions
2. **Mock external dependencies** (APIs, browser features)
3. **Test error conditions** and edge cases
4. **Update integration tests** if adding new components
5. **Maintain coverage thresholds**

### Example Test Structure

```javascript
describe('NewFeature', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  test('should handle normal case', () => {
    // Test implementation
  });
  
  test('should handle error case', () => {
    // Error handling test
  });
});
```

## Debugging Tests

### Common Issues
- **WebExtension API not mocked**: Add mock to `setup.js`
- **Async operations**: Use `await` with async test functions
- **Module imports**: Ensure proper mocking of browser globals

### Debug Commands
```bash
# Run specific test file
npm test -- video-processor.test.js

# Run tests with verbose output
npm test -- --verbose

# Run single test case
npm test -- --testNamePattern="should extract video ID"
```

## Performance

Tests are designed to run quickly:
- No actual browser launching for unit tests
- Mocked external API calls
- Minimal file system operations
- Parallel test execution where possible

Current test execution time: ~5-10 seconds for full suite