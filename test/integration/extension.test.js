// Integration tests for the complete Firefox extension

describe('Extension Integration Tests', () => {
  describe('Manifest Validation', () => {
    test('should have valid manifest.json', () => {
      const manifest = require('../../manifest.json');
      
      expect(manifest.manifest_version).toBe(2);
      expect(manifest.name).toBe('YouTube Video Bookmarker');
      expect(manifest.version).toMatch(/^\d+\.\d+\.\d+$/);
      expect(manifest.description).toBeDefined();
    });
    
    test('should have required permissions', () => {
      const manifest = require('../../manifest.json');
      const requiredPermissions = [
        'storage',
        'contextMenus',
        'activeTab',
        'tabs',
        '*://*.youtube.com/*'
      ];
      
      requiredPermissions.forEach(permission => {
        expect(manifest.permissions).toContain(permission);
      });
    });
    
    test('should have background scripts defined', () => {
      const manifest = require('../../manifest.json');
      
      expect(manifest.background).toBeDefined();
      expect(manifest.background.scripts).toBeInstanceOf(Array);
      expect(manifest.background.scripts.length).toBeGreaterThan(0);
    });
    
    test('should have content scripts for YouTube', () => {
      const manifest = require('../../manifest.json');
      
      expect(manifest.content_scripts).toBeInstanceOf(Array);
      expect(manifest.content_scripts.length).toBeGreaterThan(0);
      
      const youtubeScript = manifest.content_scripts.find(
        script => script.matches.includes('*://*.youtube.com/*')
      );
      
      expect(youtubeScript).toBeDefined();
      expect(youtubeScript.js).toBeInstanceOf(Array);
    });
  });
  
  describe('Script Loading', () => {
    test('should load all background scripts without errors', () => {
      const manifest = require('../../manifest.json');
      const fs = require('fs');
      const path = require('path');
      
      manifest.background.scripts.forEach(scriptPath => {
        const fullPath = path.join(__dirname, '../../', scriptPath);
        
        // Check if file exists
        expect(fs.existsSync(fullPath)).toBe(true);
        
        // Check if file has content
        const content = fs.readFileSync(fullPath, 'utf8');
        expect(content.length).toBeGreaterThan(0);
        
        // Basic syntax check - should not throw
        expect(() => {
          // This won't execute the code, just parse it
          new Function(content);
        }).not.toThrow();
      });
    });
    
    test('should load content scripts without errors', () => {
      const manifest = require('../../manifest.json');
      const fs = require('fs');
      const path = require('path');
      
      manifest.content_scripts.forEach(contentScript => {
        contentScript.js.forEach(scriptPath => {
          const fullPath = path.join(__dirname, '../../', scriptPath);
          
          expect(fs.existsSync(fullPath)).toBe(true);
          
          const content = fs.readFileSync(fullPath, 'utf8');
          expect(content.length).toBeGreaterThan(0);
        });
      });
    });
  });
  
  describe('Extension Dependencies', () => {
    test('should have all required libraries', () => {
      const fs = require('fs');
      const path = require('path');
      
      const requiredLibraries = ['lib/nunjucks.min.js'];
      
      requiredLibraries.forEach(libPath => {
        const fullPath = path.join(__dirname, '../../', libPath);
        expect(fs.existsSync(fullPath)).toBe(true);
      });
    });
    
    test('should have service dependencies loaded in correct order', () => {
      const manifest = require('../../manifest.json');
      const scripts = manifest.background.scripts;
      
      // Nunjucks should be loaded before nunjucks-service
      const nunjucksIndex = scripts.indexOf('lib/nunjucks.min.js');
      const serviceIndex = scripts.indexOf('services/nunjucks-service.js');
      
      expect(nunjucksIndex).toBeGreaterThan(-1);
      expect(serviceIndex).toBeGreaterThan(-1);
      expect(nunjucksIndex).toBeLessThan(serviceIndex);
      
      // Prompts should be loaded before reporter
      const promptsIndex = scripts.indexOf('prompts/investigative-reporter-prompts.js');
      const reporterIndex = scripts.indexOf('services/investigative-reporter-service.js');
      
      expect(promptsIndex).toBeGreaterThan(-1);
      expect(reporterIndex).toBeGreaterThan(-1);
      expect(promptsIndex).toBeLessThan(reporterIndex);
    });
  });
  
  describe('Background Script Integration', () => {
    beforeEach(() => {
      // Reset global state before each test
      global.window = {
        INVESTIGATIVE_REPORTER_PROMPTS: {
          SYSTEM_PROMPT: 'Test system prompt',
          USER_PROMPT_TEMPLATE: 'Process: {{ transcript }}'
        }
      };
      
      global.transformTranscriptToArticle = undefined;
    });
    
    test('should initialize background services', () => {
      // Mock loading investigative reporter prompts
      global.window.INVESTIGATIVE_REPORTER_PROMPTS = {
        SYSTEM_PROMPT: 'Test system prompt',
        USER_PROMPT_TEMPLATE: 'Process: {{ transcript }}'
      };
      
      // Mock the browser investigative reporter
      const mockTransformFunction = async (transcript) => {
        return `Processed: ${transcript}`;
      };
      
      global.transformTranscriptToArticle = mockTransformFunction;
      
      expect(global.transformTranscriptToArticle).toBeDefined();
      expect(typeof global.transformTranscriptToArticle).toBe('function');
    });
    
    test('should have context menu creation function', () => {
      // Mock context menu creation
      const createContextMenus = () => {
        return browser.contextMenus.create({
          id: 'save-youtube-video',
          title: 'Save YouTube Video',
          contexts: ['all']
        });
      };
      
      browser.contextMenus.create.mockReturnValue('menu-id');
      
      const result = createContextMenus();
      
      expect(result).toBe('menu-id');
      expect(browser.contextMenus.create).toHaveBeenCalledWith({
        id: 'save-youtube-video',
        title: 'Save YouTube Video',
        contexts: ['all']
      });
    });
  });
  
  describe('Storage Integration', () => {
    beforeEach(() => {
      jest.clearAllMocks();
      browser.storage.local.get.mockResolvedValue({ videos: [] });
      browser.storage.local.set.mockResolvedValue();
    });
    
    test('should initialize with empty video storage', async () => {
      browser.storage.local.get.mockResolvedValue({});
      
      const result = await browser.storage.local.get('videos');
      const videos = result.videos || [];
      
      expect(videos).toEqual([]);
    });
    
    test('should handle video save workflow', async () => {
      const mockVideo = {
        id: 'test123',
        url: 'https://youtube.com/watch?v=test123',
        title: 'Test Video',
        thumbnailUrl: 'https://i.ytimg.com/vi/test123/mqdefault.jpg',
        transcriptStatus: 'queued',
        dateAdded: Date.now()
      };
      
      // Simulate the complete save workflow
      const result = await browser.storage.local.get('videos');
      const videos = result.videos || [];
      videos.push(mockVideo);
      await browser.storage.local.set({ videos });
      
      expect(browser.storage.local.set).toHaveBeenCalledWith({
        videos: [mockVideo]
      });
    });
  });
  
  describe('Error Handling', () => {
    test('should handle storage errors gracefully', async () => {
      browser.storage.local.get.mockRejectedValue(new Error('Storage error'));
      
      const safeGetVideos = async () => {
        try {
          const result = await browser.storage.local.get('videos');
          return result.videos || [];
        } catch (error) {
          console.error('Storage error:', error);
          return [];
        }
      };
      
      const result = await safeGetVideos();
      
      expect(result).toEqual([]);
    });
    
    test('should handle context menu creation errors', () => {
      browser.contextMenus.create.mockImplementation(() => {
        throw new Error('Context menu error');
      });
      
      const safeCreateContextMenu = () => {
        try {
          return browser.contextMenus.create({
            id: 'test-menu',
            title: 'Test Menu',
            contexts: ['all']
          });
        } catch (error) {
          console.error('Context menu error:', error);
          return null;
        }
      };
      
      const result = safeCreateContextMenu();
      
      expect(result).toBeNull();
    });
  });
});