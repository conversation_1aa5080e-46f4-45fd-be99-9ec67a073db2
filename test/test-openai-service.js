const OpenAIService = require('../services/openai-completion-service.js');
const { Message } = require('../types.js');

async function testService() {
  console.log('🚀 Testing OpenAI Completion Service\n');

  try {
    // Initialize service
    const service = new OpenAIService();
    console.log('✅ Service initialized successfully');

    // Test 1: Simple prompt
    console.log('\n📝 Test 1: Simple prompt');
    const response1 = await service.simple('Hello!');
    console.log('Response:', response1.getContent());
    console.log('Usage:', service.formatUsage(response1.usage));

    // Test 2: With system prompt
    console.log('\n📝 Test 2: With system prompt');
    const response2 = await service.simple(
      'Explain JavaScript in one sentence.',
      'You are a concise technical instructor.'
    );
    console.log('Response:', response2.getContent());
    console.log('Token usage:', response2.getTotalTokens());

    // Test 3: Full chat with custom options
    console.log('\n📝 Test 3: Full chat with custom options');
    const messages = [
      service.createSystemMessage('You are a helpful coding assistant.'),
      service.createUserMessage('Write a simple function to add two numbers.')
    ];
    
    const response3 = await service.chat(messages, {
      temperature: 0.3,
      max_tokens: 100
    });
    
    console.log('Response:', response3.getContent());
    console.log('Finish reason:', response3.getFinishReason());
    console.log('Model used:', response3.model);

    // Test 4: Cost calculation
    console.log('\n💰 Test 4: Cost calculation');
    const cost = service.calculateCost(response3.usage, response3.model);
    console.log('Cost breakdown:', cost);

    // Test 5: Multiple messages conversation
    console.log('\n💬 Test 5: Multi-turn conversation');
    const conversation = [
      Message.system('You are a helpful assistant.'),
      Message.user('What is 2+2?'),
      Message.assistant('2+2 equals 4.'),
      Message.user('What about 4+4?')
    ];
    
    const response5 = await service.chat(conversation, { temperature: 0.1 });
    console.log('Response:', response5.getContent());
    
    // Test 6: Error handling
    console.log('\n❌ Test 6: Error handling');
    try {
      await service.chat([]); // Empty messages should fail
    } catch (error) {
      console.log('Expected error caught:', error.name, '-', error.message);
    }

    console.log('\n🎉 All tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.name, '-', error.message);
    if (error.statusCode) {
      console.error('Status code:', error.statusCode);
    }
  }
}

// Run tests
testService();