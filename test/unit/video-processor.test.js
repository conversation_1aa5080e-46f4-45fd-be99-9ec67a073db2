// Unit tests for video processing functionality
// Tests actual video processor utility functions

const VideoProcessor = require('../../lib/video-processor.js');

describe('Video Processing', () => {
  describe('getVideoId', () => {
    test('should extract video ID from standard YouTube watch URL', () => {
      const url = 'https://www.youtube.com/watch?v=dQw4w9WgXcQ';
      const result = VideoProcessor.getVideoId(url);
      expect(result).toBe('dQw4w9WgXcQ');
    });
    
    test('should extract video ID from youtu.be short URL', () => {
      const url = 'https://youtu.be/dQw4w9WgXcQ';
      const result = VideoProcessor.getVideoId(url);
      expect(result).toBe('dQw4w9WgXcQ');
    });
    
    test('should extract video ID from YouTube shorts URL', () => {
      const url = 'https://www.youtube.com/shorts/dQw4w9WgXcQ';
      const result = VideoProcessor.getVideoId(url);
      expect(result).toBe('dQw4w9WgXcQ');
    });
    
    test('should handle URLs with additional parameters', () => {
      const url = 'https://www.youtube.com/watch?v=dQw4w9WgXcQ&t=30s&list=PLExample';
      const result = VideoProcessor.getVideoId(url);
      expect(result).toBe('dQw4w9WgXcQ');
    });

    test('should handle embed URLs', () => {
      const url = 'https://www.youtube.com/embed/dQw4w9WgXcQ';
      const result = VideoProcessor.getVideoId(url);
      expect(result).toBe('dQw4w9WgXcQ');
    });
    
    test('should return null for invalid YouTube URLs', () => {
      const invalidUrls = [
        'https://example.com/watch?v=dQw4w9WgXcQ',
        'not-a-url',
        '',
        'https://youtube.com/channel/UCExample',
        'https://youtube.com',
        null,
        undefined
      ];
      
      invalidUrls.forEach(url => {
        const result = VideoProcessor.getVideoId(url);
        expect(result).toBeNull();
      });
    });
  });

  describe('isValidVideoId', () => {
    test('should validate correct video IDs', () => {
      const validIds = [
        'dQw4w9WgXcQ',
        'abcdefghijk',
        'A1B2C3D4E5F',
        '123456789_-'
      ];

      validIds.forEach(id => {
        const result = VideoProcessor.isValidVideoId(id);
        expect(result).toBe(true);
      });
    });

    test('should reject invalid video IDs', () => {
      const invalidIds = [
        '',
        'short',
        'toolong12345',
        'invalid@id',
        'has spaces',
        null,
        undefined,
        123
      ];

      invalidIds.forEach(id => {
        const result = VideoProcessor.isValidVideoId(id);
        expect(result).toBe(false);
      });
    });
  });
  
  describe('generateVideoUrl', () => {
    test('should generate correct YouTube URL for video ID', () => {
      const videoId = 'dQw4w9WgXcQ';
      const result = VideoProcessor.generateVideoUrl(videoId);
      expect(result).toBe('https://www.youtube.com/watch?v=dQw4w9WgXcQ');
    });

    test('should throw error for invalid video ID', () => {
      expect(() => VideoProcessor.generateVideoUrl('invalid')).toThrow('Invalid video ID');
      expect(() => VideoProcessor.generateVideoUrl('')).toThrow('Invalid video ID');
      expect(() => VideoProcessor.generateVideoUrl(null)).toThrow('Invalid video ID');
    });
  });
  
  describe('generateThumbnailUrl', () => {
    test('should generate correct thumbnail URL for video ID', () => {
      const videoId = 'dQw4w9WgXcQ';
      const result = VideoProcessor.generateThumbnailUrl(videoId);
      expect(result).toBe('https://i.ytimg.com/vi/dQw4w9WgXcQ/mqdefault.jpg');
    });

    test('should support different quality options', () => {
      const videoId = 'dQw4w9WgXcQ';
      const result = VideoProcessor.generateThumbnailUrl(videoId, 'hqdefault');
      expect(result).toBe('https://i.ytimg.com/vi/dQw4w9WgXcQ/hqdefault.jpg');
    });
    
    test('should throw error for invalid video ID', () => {
      expect(() => VideoProcessor.generateThumbnailUrl('invalid')).toThrow('Invalid video ID');
    });
  });

  describe('createVideoObject', () => {
    test('should create video object with required fields', () => {
      const videoId = 'dQw4w9WgXcQ';
      const title = 'Test Video';
      
      const result = VideoProcessor.createVideoObject(videoId, title);
      
      expect(result).toMatchObject({
        id: videoId,
        url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        title: title,
        thumbnailUrl: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/mqdefault.jpg',
        transcript: null,
        article: null,
        transcriptStatus: 'pending'
      });
      expect(result.dateAdded).toBeGreaterThan(0);
    });

    test('should use default title when none provided', () => {
      const videoId = 'dQw4w9WgXcQ';
      const result = VideoProcessor.createVideoObject(videoId);
      expect(result.title).toBe('YouTube Video - dQw4w9WgXcQ');
    });

    test('should include metadata when provided', () => {
      const videoId = 'dQw4w9WgXcQ';
      const metadata = {
        duration: '3:35',
        channelName: 'Test Channel',
        viewCount: '1.2M views'
      };
      
      const result = VideoProcessor.createVideoObject(videoId, 'Test', metadata);
      
      expect(result.duration).toBe('3:35');
      expect(result.channelName).toBe('Test Channel');
      expect(result.viewCount).toBe('1.2M views');
    });

    test('should throw error for invalid video ID', () => {
      expect(() => VideoProcessor.createVideoObject('invalid')).toThrow('Invalid video ID');
    });
  });

  describe('extractVideoIdFromContext', () => {
    test('should extract video ID from linkUrl', () => {
      const contextInfo = {
        linkUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        srcUrl: null,
        pageUrl: 'https://www.youtube.com/'
      };
      
      const result = VideoProcessor.extractVideoIdFromContext(contextInfo);
      expect(result).toBe('dQw4w9WgXcQ');
    });

    test('should extract video ID from srcUrl when linkUrl fails', () => {
      const contextInfo = {
        linkUrl: 'https://example.com',
        srcUrl: 'https://youtu.be/dQw4w9WgXcQ',
        pageUrl: 'https://www.youtube.com/'
      };
      
      const result = VideoProcessor.extractVideoIdFromContext(contextInfo);
      expect(result).toBe('dQw4w9WgXcQ');
    });

    test('should return null for empty context', () => {
      const result = VideoProcessor.extractVideoIdFromContext({});
      expect(result).toBeNull();
    });

    test('should return null for null context', () => {
      const result = VideoProcessor.extractVideoIdFromContext(null);
      expect(result).toBeNull();
    });
  });

  describe('video list operations', () => {
    const mockVideos = [
      { id: 'video1', title: 'Video 1' },
      { id: 'video2', title: 'Video 2' },
      { id: 'video3', title: 'Video 3' }
    ];

    describe('videoExists', () => {
      test('should return true for existing video', () => {
        const result = VideoProcessor.videoExists(mockVideos, 'video2');
        expect(result).toBe(true);
      });

      test('should return false for non-existing video', () => {
        const result = VideoProcessor.videoExists(mockVideos, 'nonexistent');
        expect(result).toBe(false);
      });

      test('should handle empty arrays', () => {
        const result = VideoProcessor.videoExists([], 'video1');
        expect(result).toBe(false);
      });

      test('should handle invalid inputs', () => {
        expect(VideoProcessor.videoExists(null, 'video1')).toBe(false);
        expect(VideoProcessor.videoExists(mockVideos, null)).toBe(false);
      });
    });

    describe('findVideoById', () => {
      test('should find video by ID', () => {
        const result = VideoProcessor.findVideoById(mockVideos, 'video2');
        expect(result).toEqual({ id: 'video2', title: 'Video 2' });
      });

      test('should return null for non-existing video', () => {
        const result = VideoProcessor.findVideoById(mockVideos, 'nonexistent');
        expect(result).toBeNull();
      });

      test('should handle invalid inputs', () => {
        expect(VideoProcessor.findVideoById(null, 'video1')).toBeNull();
        expect(VideoProcessor.findVideoById(mockVideos, null)).toBeNull();
      });
    });

    describe('updateVideoInList', () => {
      test('should update video properties', () => {
        const updates = { title: 'Updated Video 2', transcriptStatus: 'ready' };
        const result = VideoProcessor.updateVideoInList(mockVideos, 'video2', updates);
        
        expect(result[1]).toEqual({
          id: 'video2',
          title: 'Updated Video 2',
          transcriptStatus: 'ready'
        });
        expect(result[0]).toEqual(mockVideos[0]); // Other videos unchanged
        expect(result[2]).toEqual(mockVideos[2]);
      });

      test('should return original array for non-existing video', () => {
        const updates = { title: 'Updated' };
        const result = VideoProcessor.updateVideoInList(mockVideos, 'nonexistent', updates);
        expect(result).toEqual(mockVideos);
      });

      test('should handle invalid inputs', () => {
        const result = VideoProcessor.updateVideoInList(null, 'video1', {});
        expect(result).toBeNull();
      });
    });

    describe('removeVideoFromList', () => {
      test('should remove video by ID', () => {
        const result = VideoProcessor.removeVideoFromList(mockVideos, 'video2');
        expect(result).toHaveLength(2);
        expect(result).toEqual([
          { id: 'video1', title: 'Video 1' },
          { id: 'video3', title: 'Video 3' }
        ]);
      });

      test('should return original array for non-existing video', () => {
        const result = VideoProcessor.removeVideoFromList(mockVideos, 'nonexistent');
        expect(result).toEqual(mockVideos);
      });

      test('should handle invalid inputs', () => {
        const result = VideoProcessor.removeVideoFromList(null, 'video1');
        expect(result).toBeNull();
      });
    });
  });
});