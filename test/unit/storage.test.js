// Unit tests for browser storage operations
// Tests actual storage module functions

const StorageManager = require('../../background/storage.js');

describe('Video Storage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  describe('getVideos', () => {
    test('should retrieve all videos from storage', async () => {
      const mockVideos = [
        { id: 'video1', title: 'Video 1' },
        { id: 'video2', title: 'Video 2' }
      ];
      
      browser.storage.local.get.mockResolvedValue({ videos: mockVideos });
      
      const result = await StorageManager.getVideos();
      
      expect(browser.storage.local.get).toHaveBeenCalledWith('videos');
      expect(result).toEqual(mockVideos);
    });
    
    test('should return empty array when no videos stored', async () => {
      browser.storage.local.get.mockResolvedValue({});
      
      const result = await StorageManager.getVideos();
      
      expect(result).toEqual([]);
    });

    test('should handle storage errors gracefully', async () => {
      browser.storage.local.get.mockRejectedValue(new Error('Storage error'));
      
      const result = await StorageManager.getVideos();
      
      expect(result).toEqual([]);
    });
  });
  
  describe('saveVideo', () => {
    test('should save a new video to empty storage', async () => {
      const mockVideo = {
        id: 'dQw4w9WgXcQ',
        url: 'https://youtube.com/watch?v=dQw4w9WgXcQ',
        title: 'Test Video',
        thumbnailUrl: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/mqdefault.jpg',
        transcriptStatus: 'queued',
        dateAdded: Date.now()
      };
      
      // Mock empty storage
      browser.storage.local.get.mockResolvedValue({ videos: [] });
      browser.storage.local.set.mockResolvedValue();
      
      const result = await StorageManager.saveVideo(mockVideo);
      
      expect(browser.storage.local.get).toHaveBeenCalledWith('videos');
      expect(browser.storage.local.set).toHaveBeenCalledWith({
        videos: [mockVideo]
      });
      expect(result).toBe(true);
    });
    
    test('should add video to existing videos array', async () => {
      const existingVideo = {
        id: 'existing123',
        title: 'Existing Video',
        url: 'https://youtube.com/watch?v=existing123'
      };
      
      const newVideo = {
        id: 'new456',
        title: 'New Video', 
        url: 'https://youtube.com/watch?v=new456'
      };
      
      // Mock storage with existing video
      browser.storage.local.get.mockResolvedValue({ videos: [existingVideo] });
      browser.storage.local.set.mockResolvedValue();
      
      const result = await StorageManager.saveVideo(newVideo);
      
      expect(browser.storage.local.set).toHaveBeenCalledWith({
        videos: [newVideo, existingVideo] // New video goes first
      });
      expect(result).toBe(true);
    });
    
    test('should not duplicate videos with same ID', async () => {
      const video = {
        id: 'dQw4w9WgXcQ',
        title: 'Test Video',
        url: 'https://youtube.com/watch?v=dQw4w9WgXcQ'
      };
      
      // Mock storage with existing video
      browser.storage.local.get.mockResolvedValue({ videos: [video] });
      
      const result = await StorageManager.saveVideo(video);
      
      expect(result).toBe(false);
      expect(browser.storage.local.set).not.toHaveBeenCalled();
    });

    test('should handle storage errors gracefully', async () => {
      const video = { id: 'test', title: 'Test' };
      browser.storage.local.get.mockRejectedValue(new Error('Storage error'));
      browser.storage.local.set.mockRejectedValue(new Error('Set error'));
      
      const result = await StorageManager.saveVideo(video);
      
      expect(result).toBe(false);
    });
  });
  
  describe('updateVideo', () => {
    test('should update existing video', async () => {
      const videos = [
        { id: 'video1', title: 'Video 1', transcriptStatus: 'queued' },
        { id: 'video2', title: 'Video 2', transcriptStatus: 'queued' }
      ];
      
      browser.storage.local.get.mockResolvedValue({ videos });
      browser.storage.local.set.mockResolvedValue();
      
      const updates = { transcriptStatus: 'success', transcript: 'Mock transcript' };
      const result = await StorageManager.updateVideo('video1', updates);
      
      expect(browser.storage.local.set).toHaveBeenCalledWith({
        videos: [
          { 
            id: 'video1', 
            title: 'Video 1', 
            transcriptStatus: 'success',
            transcript: 'Mock transcript'
          },
          { id: 'video2', title: 'Video 2', transcriptStatus: 'queued' }
        ]
      });
      expect(result).toBe(true);
    });

    test('should handle updating non-existent video', async () => {
      browser.storage.local.get.mockResolvedValue({ videos: [] });
      
      const result = await StorageManager.updateVideo('nonexistent', { title: 'Updated' });
      
      expect(result).toBe(false);
      expect(browser.storage.local.set).not.toHaveBeenCalled();
    });
  });
  
  describe('removeVideo', () => {
    test('should remove video by ID', async () => {
      const videos = [
        { id: 'keep1', title: 'Keep This' },
        { id: 'remove', title: 'Remove This' },
        { id: 'keep2', title: 'Keep This Too' }
      ];
      
      browser.storage.local.get.mockResolvedValue({ videos });
      browser.storage.local.set.mockResolvedValue();
      
      const result = await StorageManager.removeVideo('remove');
      
      expect(browser.storage.local.set).toHaveBeenCalledWith({
        videos: [
          { id: 'keep1', title: 'Keep This' },
          { id: 'keep2', title: 'Keep This Too' }
        ]
      });
      expect(result).toBe(true);
    });
    
    test('should handle removing non-existent video', async () => {
      const videos = [{ id: 'existing', title: 'Existing Video' }];
      
      browser.storage.local.get.mockResolvedValue({ videos });
      
      const result = await StorageManager.removeVideo('nonexistent');
      
      expect(result).toBe(false);
      expect(browser.storage.local.set).not.toHaveBeenCalled();
    });
  });
  
  describe('updateVideoStatus', () => {
    test('should update transcript status for video', async () => {
      const videos = [
        { id: 'video1', title: 'Video 1', transcriptStatus: 'queued' }
      ];
      
      browser.storage.local.get.mockResolvedValue({ videos });
      browser.storage.local.set.mockResolvedValue();
      
      const result = await StorageManager.updateVideoStatus('video1', 'success', { 
        transcript: 'Mock transcript content' 
      });
      
      expect(browser.storage.local.set).toHaveBeenCalledWith({
        videos: [
          { 
            id: 'video1', 
            title: 'Video 1', 
            transcriptStatus: 'success',
            transcript: 'Mock transcript content'
          }
        ]
      });
      expect(result).toBe(true);
    });
  });

  describe('API Key Management', () => {
    test('should get API key from storage', async () => {
      browser.storage.local.get.mockResolvedValue({ apify_api_key: 'test-key' });
      
      const result = await StorageManager.getApiKey('apify_api_key');
      
      expect(browser.storage.local.get).toHaveBeenCalledWith('apify_api_key');
      expect(result).toBe('test-key');
    });

    test('should return null for missing API key', async () => {
      browser.storage.local.get.mockResolvedValue({});
      
      const result = await StorageManager.getApiKey('missing_key');
      
      expect(result).toBeNull();
    });

    test('should set API key in storage', async () => {
      browser.storage.local.set.mockResolvedValue();
      
      const result = await StorageManager.setApiKey('openai_api_key', 'sk-test123');
      
      expect(browser.storage.local.set).toHaveBeenCalledWith({ openai_api_key: 'sk-test123' });
      expect(result).toBe(true);
    });

    test('should check API keys status', async () => {
      browser.storage.local.get.mockResolvedValue({
        apify_api_key: 'test-apify',
        openai_api_key: 'test-openai',
        // gemini_api_key missing
        api_keys_configured: true
      });
      
      const result = await StorageManager.checkApiKeysStatus();
      
      expect(result).toEqual({
        hasApifyKey: true,
        hasOpenAIKey: true,
        hasGeminiKey: false,
        isConfigured: true,
        isSkipped: false,
        hasAllKeys: false
      });
    });
  });

  describe('clearAllData', () => {
    test('should clear all storage data', async () => {
      browser.storage.local.clear.mockResolvedValue();
      
      const result = await StorageManager.clearAllData();
      
      expect(browser.storage.local.clear).toHaveBeenCalled();
      expect(result).toBe(true);
    });

    test('should handle clear errors gracefully', async () => {
      browser.storage.local.clear.mockRejectedValue(new Error('Clear failed'));
      
      const result = await StorageManager.clearAllData();
      
      expect(result).toBe(false);
    });
  });
});