// Unit tests for browser investigative reporter functionality

describe('Investigative Reporter', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Reset browser storage mock
    browser.storage.local.get.mockResolvedValue({
      openai_api_key: 'test-api-key'
    });
    
    // Set up window global with prompts
    global.window = global.window || {};
    global.window.INVESTIGATIVE_REPORTER_PROMPTS = {
      SYSTEM_PROMPT: 'You are a test system prompt for investigative reporter.',
      USER_PROMPT_TEMPLATE: 'Transform this transcript: {{ transcript }}'
    };
  });
  
  describe('transformTranscriptToArticle', () => {
    // Mock the transformTranscriptToArticle function for testing
    const mockTransformTranscriptToArticle = async (transcript) => {
      if (!transcript || typeof transcript !== "string" || transcript.trim() === "") {
        throw new Error("Transcript must be a non-empty string");
      }
      
      // Get API key from storage
      const result = await browser.storage.local.get("openai_api_key");
      const apiKey = result.openai_api_key;
      
      if (!apiKey) {
        throw new Error("OpenAI API key not configured");
      }
      
      const openai = new BrowserOpenAIService({ apiKey });
      
      // Get prompts from shared templates
      const { SYSTEM_PROMPT, USER_PROMPT_TEMPLATE } = global.window.INVESTIGATIVE_REPORTER_PROMPTS;
      
      const nunjucksService = new NunjucksService();
      const userPrompt = nunjucksService.renderString(USER_PROMPT_TEMPLATE, { transcript });
      
      const response = await openai.withSystem(SYSTEM_PROMPT, userPrompt, {
        model: "gpt-4o-mini",
        temperature: 0.7,
        max_tokens: 2000,
      });
      
      return response.getContent();
    };
    
    test('should process valid transcript and return article', async () => {
      const transcript = "This is a test transcript with some content about technology.";
      
      const result = await mockTransformTranscriptToArticle(transcript);
      
      expect(browser.storage.local.get).toHaveBeenCalledWith("openai_api_key");
      expect(result).toContain("Mock AI response");
      expect(typeof result).toBe('string');
    });
    
    test('should reject empty transcript', async () => {
      await expect(mockTransformTranscriptToArticle("")).rejects.toThrow(
        "Transcript must be a non-empty string"
      );
    });
    
    test('should reject null transcript', async () => {
      await expect(mockTransformTranscriptToArticle(null)).rejects.toThrow(
        "Transcript must be a non-empty string"
      );
    });
    
    test('should reject non-string transcript', async () => {
      await expect(mockTransformTranscriptToArticle(123)).rejects.toThrow(
        "Transcript must be a non-empty string"
      );
    });
    
    test('should reject whitespace-only transcript', async () => {
      await expect(mockTransformTranscriptToArticle("   \n\t  ")).rejects.toThrow(
        "Transcript must be a non-empty string"
      );
    });
    
    test('should throw error when API key not configured', async () => {
      // Mock storage without API key
      browser.storage.local.get.mockResolvedValue({});
      
      const transcript = "Valid transcript content";
      
      await expect(mockTransformTranscriptToArticle(transcript)).rejects.toThrow(
        "OpenAI API key not configured"
      );
    });
    
    test('should use Nunjucks template rendering', async () => {
      const transcript = "Test transcript content for template rendering";
      
      await mockTransformTranscriptToArticle(transcript);
      
      // Verify that the template was processed with the transcript
      expect(global.window.INVESTIGATIVE_REPORTER_PROMPTS.USER_PROMPT_TEMPLATE).toContain('{{ transcript }}');
    });
    
    test('should handle long transcripts', async () => {
      const longTranscript = "A".repeat(10000); // 10k character transcript
      
      const result = await mockTransformTranscriptToArticle(longTranscript);
      
      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
    });
    
    test('should handle transcripts with special characters', async () => {
      const specialTranscript = "Transcript with émojis 🎉 and spëcial chars & HTML <tags>";
      
      const result = await mockTransformTranscriptToArticle(specialTranscript);
      
      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
    });
  });
  
  describe('Nunjucks Template Processing', () => {
    test('should correctly render template with transcript variable', () => {
      const nunjucks = new NunjucksService();
      const template = "Process this transcript: {{ transcript }}";
      const variables = { transcript: "Test content" };
      
      const result = nunjucks.renderString(template, variables);
      
      expect(result).toBe("Process this transcript: Test content");
    });
    
    test('should handle missing template variables', () => {
      const nunjucks = new NunjucksService();
      const template = "Process: {{ transcript }} and {{ missing }}";
      const variables = { transcript: "Test content" };
      
      const result = nunjucks.renderString(template, variables);
      
      expect(result).toBe("Process: Test content and {{ missing }}");
    });
    
    test('should handle empty template variables', () => {
      const nunjucks = new NunjucksService();
      const template = "Content: {{ transcript }}";
      const variables = { transcript: "" };
      
      const result = nunjucks.renderString(template, variables);
      
      expect(result).toBe("Content: ");
    });
  });
  
  describe('BrowserOpenAIService Integration', () => {
    test('should create service with API key', () => {
      const apiKey = 'test-api-key';
      const service = new BrowserOpenAIService({ apiKey });
      
      expect(service.config.apiKey).toBe(apiKey);
    });
    
    test('should call withSystem method with correct parameters', async () => {
      const service = new BrowserOpenAIService({ apiKey: 'test-key' });
      
      const systemPrompt = "You are a test assistant";
      const userPrompt = "Process this content";
      const options = { model: "gpt-4o-mini", temperature: 0.7 };
      
      const result = await service.withSystem(systemPrompt, userPrompt, options);
      
      expect(result.getContent()).toContain("Mock AI response");
    });
  });
});