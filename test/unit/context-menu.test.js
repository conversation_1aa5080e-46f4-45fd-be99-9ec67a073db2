// Unit tests for context menu functionality
// Tests actual context menu module functions

const ContextMenuManager = require('../../background/context-menu.js');

describe('Context Menu', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset context menu state for each test
    ContextMenuManager.resetContextMenuState();
    // Clear processing videos state for each test
    ContextMenuManager.clearProcessingVideos();
  });
  
  describe('Context Menu Creation', () => {
    test('should create context menu successfully', () => {
      browser.contextMenus.create.mockImplementation((config, callback) => {
        callback(); // Simulate successful creation
        return 'menu-id';
      });
      
      const result = ContextMenuManager.createContextMenus();
      
      expect(browser.contextMenus.create).toHaveBeenCalledWith({
        id: 'bookmark-youtube-video',
        title: 'Bookmark YouTube Video',
        contexts: ['page', 'link', 'image', 'video', 'frame'],
        documentUrlPatterns: ['*://*.youtube.com/*']
      }, expect.any(Function));
      expect(result).toBe(true);
      expect(ContextMenuManager.isContextMenuCreated()).toBe(true);
    });
    
    test('should handle context menu creation failure', () => {
      browser.runtime.lastError = { message: 'Creation failed' };
      browser.contextMenus.create.mockImplementation((config, callback) => {
        callback(); // Simulate callback with error
        return null;
      });
      
      const result = ContextMenuManager.createContextMenus();
      
      expect(result).toBe(true); // Function still returns true, but menu isn't marked as created
      expect(ContextMenuManager.isContextMenuCreated()).toBe(false);
      
      // Clean up
      delete browser.runtime.lastError;
    });

    test('should skip creation if menu already exists', () => {
      // Create menu first time
      browser.contextMenus.create.mockImplementation((config, callback) => {
        callback();
        return 'menu-id';
      });
      ContextMenuManager.createContextMenus();
      
      // Try to create again
      browser.contextMenus.create.mockClear();
      const result = ContextMenuManager.createContextMenus();
      
      expect(browser.contextMenus.create).not.toHaveBeenCalled();
      expect(result).toBe(true);
    });

    test('should handle exception during creation', () => {
      browser.contextMenus.create.mockImplementation(() => {
        throw new Error('Context menu creation failed');
      });
      
      const result = ContextMenuManager.createContextMenus();
      
      expect(result).toBe(false);
      expect(ContextMenuManager.isContextMenuCreated()).toBe(false);
    });
  });

  describe('Video Processing State', () => {
    test('should track processing videos', () => {
      const videoId = 'test123';
      
      expect(ContextMenuManager.isVideoProcessing(videoId)).toBe(false);
      
      ContextMenuManager.markVideoAsProcessing(videoId);
      expect(ContextMenuManager.isVideoProcessing(videoId)).toBe(true);
      
      ContextMenuManager.markVideoAsComplete(videoId);
      expect(ContextMenuManager.isVideoProcessing(videoId)).toBe(false);
    });

    test('should auto-cleanup processing videos after timeout', () => {
      jest.useFakeTimers();
      
      const videoId = 'test123';
      
      ContextMenuManager.markVideoAsProcessing(videoId);
      expect(ContextMenuManager.isVideoProcessing(videoId)).toBe(true);
      
      // Fast-forward time by 30 seconds (the timeout duration)
      jest.advanceTimersByTime(30000);
      
      expect(ContextMenuManager.isVideoProcessing(videoId)).toBe(false);
      
      jest.useRealTimers();
    });

    test('should get processing videos list', () => {
      ContextMenuManager.markVideoAsProcessing('video1');
      ContextMenuManager.markVideoAsProcessing('video2');
      
      const processing = ContextMenuManager.getProcessingVideos();
      expect(processing.size).toBe(2);
      expect(processing.has('video1')).toBe(true);
      expect(processing.has('video2')).toBe(true);
    });

    test('should clear all processing videos', () => {
      ContextMenuManager.markVideoAsProcessing('video1');
      ContextMenuManager.markVideoAsProcessing('video2');
      
      expect(ContextMenuManager.getProcessingVideos().size).toBe(2);
      
      ContextMenuManager.clearProcessingVideos();
      expect(ContextMenuManager.getProcessingVideos().size).toBe(0);
    });
  });

  describe('Video ID Extraction', () => {
    test('should extract video ID from context menu info', async () => {
      const mockInfo = {
        linkUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        srcUrl: null,
        pageUrl: 'https://www.youtube.com/'
      };
      
      const mockTab = {
        id: 1,
        url: 'https://www.youtube.com/'
      };
      
      const result = await ContextMenuManager.extractVideoIdFromContextMenu(mockInfo, mockTab);
      
      expect(result.videoId).toBe('dQw4w9WgXcQ');
      expect(result.videoUrl).toBe('https://www.youtube.com/watch?v=dQw4w9WgXcQ');
    });

    test('should fallback to DOM extraction when URLs fail', async () => {
      const mockInfo = {
        linkUrl: null,
        srcUrl: null,
        pageUrl: 'https://www.youtube.com/watch'
      };
      
      const mockTab = {
        id: 1,
        url: 'https://www.youtube.com/watch'
      };
      
      // Mock successful script injection and message
      browser.tabs.executeScript.mockResolvedValue();
      browser.tabs.sendMessage.mockResolvedValue('extracted123');
      
      const result = await ContextMenuManager.extractVideoIdFromContextMenu(mockInfo, mockTab);
      
      expect(result.videoId).toBe('extracted123');
      expect(result.videoUrl).toBe('https://www.youtube.com/watch?v=extracted123');
      expect(browser.tabs.executeScript).toHaveBeenCalled();
      expect(browser.tabs.sendMessage).toHaveBeenCalled();
    });

    test('should handle DOM extraction failure', async () => {
      const mockInfo = {
        linkUrl: null,
        srcUrl: null,
        pageUrl: 'https://www.youtube.com/watch'
      };
      
      const mockTab = {
        id: 1,
        url: 'https://www.youtube.com/watch'
      };
      
      // Mock failed script injection
      browser.tabs.executeScript.mockRejectedValue(new Error('Script injection failed'));
      // Reset the sendMessage mock to not return anything
      browser.tabs.sendMessage.mockResolvedValue(null);
      
      const result = await ContextMenuManager.extractVideoIdFromContextMenu(mockInfo, mockTab);
      
      expect(result.videoId).toBeNull();
      expect(result.videoUrl).toBeNull();
    });
  });

  describe('Video Title Extraction', () => {
    test('should get title from tab when on video page', async () => {
      const mockTab = {
        id: 1,
        url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        title: 'Test Video - YouTube'
      };
      
      const result = await ContextMenuManager.getVideoTitle(mockTab, 'dQw4w9WgXcQ');
      
      expect(result).toBe('Test Video');
    });

    test('should get title from content script when not on video page', async () => {
      const mockTab = {
        id: 1,
        url: 'https://www.youtube.com/',
        title: 'YouTube'
      };
      
      browser.tabs.executeScript.mockResolvedValue();
      browser.tabs.sendMessage.mockResolvedValue('Content Script Title');
      
      const result = await ContextMenuManager.getVideoTitle(mockTab, 'dQw4w9WgXcQ');
      
      expect(result).toBe('Content Script Title');
      expect(browser.tabs.sendMessage).toHaveBeenCalledWith(1, {
        action: 'getVideoTitleForId',
        videoId: 'dQw4w9WgXcQ'
      });
    });

    test('should use default title when extraction fails', async () => {
      const mockTab = {
        id: 1,
        url: 'https://www.youtube.com/',
        title: 'YouTube'
      };
      
      browser.tabs.executeScript.mockRejectedValue(new Error('Script failed'));
      browser.tabs.sendMessage.mockRejectedValue(new Error('Message failed'));
      
      const result = await ContextMenuManager.getVideoTitle(mockTab, 'dQw4w9WgXcQ');
      
      expect(result).toBe('YouTube Video - dQw4w9WgXcQ');
    });
  });

  describe('Video Metadata Extraction', () => {
    test('should get metadata from content script', async () => {
      const mockTab = {
        id: 1,
        url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ'
      };
      
      const mockMetadata = {
        duration: '3:35',
        channelName: 'Test Channel',
        viewCount: '1.2M views',
        uploadTime: '2 days ago'
      };
      
      browser.tabs.executeScript.mockResolvedValue();
      browser.tabs.sendMessage.mockResolvedValue(mockMetadata);
      
      const result = await ContextMenuManager.getVideoMetadata(mockTab, 'dQw4w9WgXcQ');
      
      expect(result).toEqual(mockMetadata);
      expect(browser.tabs.sendMessage).toHaveBeenCalledWith(1, {
        action: 'getVideoMetadata',
        videoId: 'dQw4w9WgXcQ'
      });
    });

    test('should return default metadata when extraction fails', async () => {
      const mockTab = {
        id: 1,
        url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ'
      };
      
      browser.tabs.executeScript.mockRejectedValue(new Error('Script failed'));
      browser.tabs.sendMessage.mockRejectedValue(new Error('Message failed'));
      
      const result = await ContextMenuManager.getVideoMetadata(mockTab, 'dQw4w9WgXcQ');
      
      expect(result).toEqual({
        duration: null,
        channelName: null,
        viewCount: null,
        uploadTime: null
      });
    });
  });
});