// Jest setup file for Firefox WebExtension testing
require('jest-webextension-mock');

// Mock browser APIs used by the extension
global.browser = {
  storage: {
    local: {
      get: jest.fn().mockResolvedValue({}),
      set: jest.fn().mockResolvedValue(),
      remove: jest.fn().mockResolvedValue(),
      clear: jest.fn().mockResolvedValue()
    }
  },
  
  contextMenus: {
    create: jest.fn().mockReturnValue('menu-id'),
    update: jest.fn().mockResolvedValue(),
    remove: jest.fn().mockResolvedValue(),
    removeAll: jest.fn().mockResolvedValue(),
    onClicked: {
      addListener: jest.fn(),
      removeListener: jest.fn(),
      hasListener: jest.fn().mockReturnValue(false)
    }
  },
  
  tabs: {
    query: jest.fn().mockResolvedValue([]),
    create: jest.fn().mockResolvedValue({ id: 1, url: 'about:blank' }),
    update: jest.fn().mockResolvedValue(),
    remove: jest.fn().mockResolvedValue(),
    get: jest.fn().mockResolvedValue({ id: 1, url: 'https://youtube.com' }),
    executeScript: jest.fn().mockResolvedValue(),
    sendMessage: jest.fn().mockResolvedValue()
  },
  
  runtime: {
    sendMessage: jest.fn().mockResolvedValue(),
    onMessage: {
      addListener: jest.fn(),
      removeListener: jest.fn(),
      hasListener: jest.fn().mockReturnValue(false)
    },
    getURL: jest.fn().mockImplementation((path) => `moz-extension://test-id/${path}`),
    getManifest: jest.fn().mockReturnValue({
      name: 'YouTube Video Bookmarker',
      version: '1.0.0'
    })
  }
};

// Load the actual investigative reporter prompts
const { SYSTEM_PROMPT, USER_PROMPT_TEMPLATE } = require('../prompts/investigative-reporter-prompts.js');

// Mock window globals used by browser scripts
global.window = {
  INVESTIGATIVE_REPORTER_PROMPTS: {
    SYSTEM_PROMPT,
    USER_PROMPT_TEMPLATE
  }
};

// Mock Nunjucks service for template rendering
global.NunjucksService = class MockNunjucksService {
  constructor() {
    this.templates = new Map();
  }
  
  renderString(template, variables = {}) {
    // Simple template replacement for testing
    let result = template;
    Object.keys(variables).forEach(key => {
      const placeholder = `{{ ${key} }}`;
      result = result.replace(new RegExp(placeholder, 'g'), variables[key]);
    });
    return result;
  }
};

// Mock BrowserOpenAIService
global.BrowserOpenAIService = class MockBrowserOpenAIService {
  constructor(config = {}) {
    this.config = config;
  }
  
  async withSystem(systemPrompt, userPrompt, options = {}) {
    return {
      getContent: () => `Mock AI response for: ${userPrompt.substring(0, 50)}...`
    };
  }
  
  async chat(messages, options = {}) {
    return {
      getContent: () => `Mock chat response for ${messages.length} messages`
    };
  }
};

// Mock fetch for API calls
global.fetch = jest.fn().mockResolvedValue({
  ok: true,
  status: 200,
  json: jest.fn().mockResolvedValue({ success: true }),
  text: jest.fn().mockResolvedValue('Mock response')
});

// Console setup for cleaner test output
const originalConsoleError = console.error;
console.error = (...args) => {
  // Suppress expected error messages during testing
  const message = args[0];
  if (typeof message === 'string' && (
    message.includes('[YT-Bookmarker]') ||
    message.includes('jest-webextension-mock')
  )) {
    return;
  }
  originalConsoleError.apply(console, args);
};

// Clear all mocks before each test
beforeEach(() => {
  jest.clearAllMocks();
  
  // Reset browser storage mock to empty state
  global.browser.storage.local.get.mockResolvedValue({});
});