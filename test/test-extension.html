<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube Extension Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-item {
            margin: 10px 0;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 3px;
        }
        .instructions {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        video {
            width: 200px;
            height: 150px;
            border: 2px solid #ccc;
        }
    </style>
</head>
<body>
    <h1>YouTube Extension Context Menu Test</h1>
    
    <div class="instructions">
        <h3>Instructions:</h3>
        <ol>
            <li>Make sure the YouTube Summarizer extension is loaded in Firefox</li>
            <li>Open Firefox Developer Tools (F12) and go to the Console tab</li>
            <li>Right-click on each test element below</li>
            <li>Look for the "Add YouTube Video to Summarizer" context menu items</li>
            <li>Check the console for detailed logging information</li>
        </ol>
        <p><strong>Note:</strong> This test page simulates YouTube-like elements but won't actually be on youtube.com, so the context menus won't appear here. Use this as a reference for what to test on actual YouTube pages.</p>
    </div>

    <div class="test-section">
        <h3>Test Elements to Try on YouTube:</h3>
        
        <div class="test-item">
            <h4>1. Video Thumbnails on Homepage</h4>
            <p>Go to youtube.com and right-click on video thumbnails in the feed</p>
        </div>
        
        <div class="test-item">
            <h4>2. Video Thumbnails in Search Results</h4>
            <p>Search for any term and right-click on video thumbnails in results</p>
        </div>
        
        <div class="test-item">
            <h4>3. Video Thumbnails in Sidebar</h4>
            <p>On a watch page, right-click on suggested video thumbnails in the sidebar</p>
        </div>
        
        <div class="test-item">
            <h4>4. Text Links to Videos</h4>
            <p>Right-click on text links that go to YouTube videos</p>
        </div>
        
        <div class="test-item">
            <h4>5. Watch Page Background</h4>
            <p>On a youtube.com/watch page, right-click on empty areas</p>
        </div>
    </div>

    <div class="test-section">
        <h3>Debug Console Commands:</h3>
        <div class="test-item">
            <h4>Run Video Analysis Script:</h4>
            <p>Copy and paste this into the console on any YouTube page:</p>
            <pre><code>// Load and run the debug script
fetch(browser.runtime.getURL('debug/debug-context-menu.js'))
  .then(response => response.text())
  .then(script => eval(script));</code></pre>
        </div>
        
        <div class="test-item">
            <h4>Check Extension Storage:</h4>
            <p>See what videos are currently stored:</p>
            <pre><code>browser.storage.local.get('videos').then(console.log);</code></pre>
        </div>
        
        <div class="test-item">
            <h4>Clear Extension Storage:</h4>
            <p>Clear all stored videos:</p>
            <pre><code>browser.storage.local.clear().then(() => console.log('Storage cleared'));</code></pre>
        </div>
    </div>

    <div class="test-section">
        <h3>Expected Console Output:</h3>
        <div class="test-item">
            <h4>When Extension Loads:</h4>
            <pre><code>[YT-Summarizer] Extension installed/updated, setting up context menus
[YT-Summarizer] Created context menu: summarizer-on-page
[YT-Summarizer] Created context menu: summarizer-on-link
[YT-Summarizer] Created context menu: summarizer-on-video
[YT-Summarizer] Created context menu: summarizer-on-all</code></pre>
        </div>
        
        <div class="test-item">
            <h4>When Context Menu is Clicked:</h4>
            <pre><code>[YT-Summarizer] Context menu clicked {menuItemId: "summarizer-on-video", pageUrl: "...", ...}
[YT-Summarizer] Video element clicked, trying multiple extraction methods
[YT-Summarizer] Attempt 1 - Page URL extraction {videoUrl: "...", videoId: "..."}
[YT-Summarizer] Successfully extracted video ID, proceeding to save ...</code></pre>
        </div>
    </div>

    <script>
        // Add some interactive elements for testing
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded. Navigate to YouTube to test the extension.');
            
            // Add click handlers to demonstrate what we're looking for
            document.addEventListener('contextmenu', function(e) {
                console.log('Context menu on test page:', {
                    target: e.target.tagName,
                    id: e.target.id,
                    className: e.target.className
                });
            });
        });
    </script>
</body>
</html>
